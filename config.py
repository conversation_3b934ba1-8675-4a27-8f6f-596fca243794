import csv
import os
from datetime import timedelta

import pytz
from decouple import config
from flask.cli import load_dotenv

BASE_DIR = os.path.abspath(os.path.dirname(__file__))
load_dotenv(os.path.join(BASE_DIR, '.env'))
# flask logging config
if os.environ.get('APP_TYPE') is not None and os.environ.get('APP_TYPE') != 'DEV':
    from logging.config import dictConfig

    dictConfig(
        {
            'version': 1,
            'formatters': {
                'default': {
                    'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
                }
            },
            'handlers': {
                'wsgi': {
                    'class': 'logging.StreamHandler',
                    'stream': 'ext://sys.stdout',
                    'formatter': 'default'
                }
            },
            'root': {
                'level': os.environ.get('LOG_LEVEL') or 'ERROR',
                'handlers': ['wsgi']
            }
        }
    )


def load_labels():
    try:
        with open('labels.csv') as csv_file:
            csv_reader = csv.reader(csv_file, delimiter=',')
            line_count = 0
            _labels = {}
            site_index = 0
            for row in csv_reader:
                if line_count == 0:
                    site_index = row.index((os.environ.get('SITE_NAME') or 'default').lower()) \
                        if (os.environ.get('SITE_NAME') or 'default').lower() in row else 0
                    line_count += 1
                else:
                    _labels[row[0]] = row[site_index]
            return _labels
    except FileNotFoundError:
        return {}


class Config(object):
    APP_TYPE = os.environ.get('APP_TYPE') or 'DEV'
    JWT_SECRET_KEY = (
            os.environ.get('JWT_SECRET_KEY') or
            os.environ.get('SECRET_KEY') or
            '$2a$12$m6VPTbfwCflVj.nlbWuEbe5u6Jn3/PDpdSkKeOkD5FfSJ0l4hAt0C'
    )
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        'DATABASE_URL'
    ) or 'postgres://postgres:K3rmit575@localhost:5432/rezibase-portland-copy'
    if SQLALCHEMY_DATABASE_URI.startswith('postgres:'):
        SQLALCHEMY_DATABASE_URI = SQLALCHEMY_DATABASE_URI.replace(
            'postgres:/', 'postgresql:/'
        )
    SQLALCHEMY_ENGINE_OPTIONS = {
        "echo_pool": "debug",
        'pool_size': 1,
        'max_overflow': 0,
        'pool_recycle': 280,
        'pool_pre_ping': True
    }

    LOG_TO_STDOUT = os.environ.get('LOG_TO_STDOUT')
    # Comment here
    # MAIL SERVER SETUP
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = config('MAIL_PORT', cast=int, default=25)
    MAIL_USE_TLS = config('MAIL_USE_TLS', cast=bool, default=False)
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get(
        'MAIL_DEFAULT_SENDER'
    ) or os.environ.get('MAIL_USERNAME')
    SUPPRESS_EMAIL = config('SUPPRESS_EMAIL', cast=bool, default=False)
    REGISTER_TRIAL_ENABLED = config('REGISTER_TRIAL_ENABLED', cast=bool, default=False)

    AWS_REGION_NAME = os.environ.get('AWS_REGION_NAME', 'ap-southeast-2')
    AWS_ACCESS_KEY = os.environ.get('AWS_ACCESS_KEY', '********************')
    AWS_SECRET_KEY = os.environ.get(
        'AWS_SECRET_KEY', 'kfaOCE1El+jLxahKheutgF/8e/xipfBSb4+RbELu'
    )
    AWS_S3_BUCKET = os.environ.get('AWS_S3_BUCKET', 'rezibase-web-app')

    ADMINS = ['<EMAIL>']
    LANGUAGES = ['en', 'es']

    MS_TRANSLATOR_KEY = os.environ.get('MS_TRANSLATOR_KEY')
    POSTS_PER_PAGE = 25
    PATIENTS_PER_PAGE = 25
    # GEO_DB = os.path.join(BASE_DIR, 'GeoLite2-Country.mmdb')
    GEO_ENABLED = True
    FLASK_DEBUG = True if APP_TYPE == 'DEV' else False
    DEBUG = FLASK_DEBUG
    SECRET_KEY = 'clb##Xh7k35Bnrko=NUJ'
    PASSWORD_SINGLE_HASH = True
    # auto logoff after 30 mts
    SESSION_PERMANENT = True
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=1)
    SESSION_REFRESH_EACH_REQUEST = True
    # Flask-Security config
    SECURITY_URL_PREFIX = "/admin"
    SECURITY_PASSWORD_HASH = "pbkdf2_sha512"
    SECURITY_PASSWORD_SALT = "ATGUOHAELKiubahiughaerGOJAEGj"

    # Flask-Security URLs, overridden because they don't put a / at the end
    SECURITY_LOGIN_URL = "/login/"
    SECURITY_LOGOUT_URL = "/logout/"
    SECURITY_REGISTER_URL = "/register/"

    SECURITY_POST_LOGIN_VIEW = "/admin/"
    SECURITY_POST_LOGOUT_VIEW = "/admin/"
    SECURITY_POST_REGISTER_VIEW = "/admin/"
    SECURITY_POST_RESET_VIEW = '/login/'
    SECURITY_POST_CONFIRM_VIEW = '/approval_wait/'
    SECURITY_RECOVERABLE = True
    SECURITY_EMAIL_SENDER = MAIL_DEFAULT_SENDER
    SECURITY_CONFIRMABLE = True
    SECURITY_TRACKABLE = True
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    # SECURITY_PASSWORD_COMPLEXITY_CHECKER = "zxcvbn"
    # SECURITY_TRACKABLE = True

    TEMPLATES_AUTO_RELOAD = True

    # Flask-Security features
    SECURITY_REGISTERABLE = False
    SECURITY_SEND_REGISTER_EMAIL = False
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    SECURITY_RESET_PASSWORD_WITHIN = "30 minutes"

    SENTRY_KEY = os.environ.get(
        'SENTRY_KEY'
    ) or 'https://<EMAIL>/5672554'
    SENTRY_JS_TSAMPLE_RATE = os.environ.get('SENTRY_JS_TSAMPLE_RATE') or 0
    SENTRY_JS_TSAMPLER = os.environ.get('SENTRY_JS_TSAMPLER') or 0
    SENTRY_PY_TSAMPLE_RATE = os.environ.get('SENTRY_PY_TSAMPLE_RATE') or 0
    SENTRY_PY_TSAMPLER = os.environ.get('SENTRY_PY_TSAMPLER') or 0
    SENTRY_PROJECT = os.environ.get('SENTRY_PROJECT')
    SENTRY_ENVIRON = os.environ.get('SENTRY_ENVIRON') or 'production'

    SLACK_API_TOKEN = '****************************************************************************'

    SITE_ADMIN_THEME_DIR_NAME = os.environ.get(
        'SITE_ADMIN_THEME_DIR_NAME'
    ) or 'default'
    SITE_NAME = os.environ.get('SITE_NAME') or 'default'
    CLIENT_NAME = os.environ.get('CLIENT_NAME') or 'default'
    SITE_LOCATION = os.environ.get('SITE_LOCATION') or 'default'
    SITE_URL = os.environ.get(
        'SITE_URL'
    ) or 'https://cardiobase.com/clinibase/'
    LOCAL_TIMEZONE = 'Australia/Melbourne'
    CURRENT_TIMEZONE = pytz.timezone(LOCAL_TIMEZONE)

    OTP_EXPIRY = os.environ.get('OTP_EXPIRY') or 24  # OTP EXPIRY IN MONTHS

    SITE_TEXT = load_labels()

    SECOND_LOGO = os.environ.get('SECOND_LOGO')

    SESSION_CONFIG = [
        # Third session will store any other values set on the Flask session on it's own secure cookie
        {
            'cookie_name': 'data',
            'session_type': 'secure_cookie',
            'session_fields': 'auto'
        },

    ]

    CSP = {
        'default-src': [
            '\'self\'',
            '*.amazonaws.com', 'cdn.datatables.net', 'stackpath.bootstrapcdn.com'
        ],
        'img-src': '*',

        'script-src': [
            '\'self\'',
            '*.amazonaws.com', 'cdn.datatables.net', '*.bootstrapcdn.com', 'cdnjs.cloudflare.com', 'www.gstatic.com',
            'api.addressfinder.io'
        ],
        'style-src': [
            '\'self\'',
            '*.amazonaws.com', 'cdn.datatables.net', '*.bootstrapcdn.com', 'cdnjs.cloudflare.com', 'www.gstatic.com',
            'api.addressfinder.io'
        ],
    }
    FLASK_ADMIN_FLUID_LAYOUT = True

    WTF_CSRF_TIME_LIMIT = 3600 * 24
    WTF_CSRF_ENABLED = False
    TESTING = False

    LEAD_PARTICIPANT = os.environ.get('LEAD_PARTICIPANT') or True

    # MAILCHIMP SETUP
    MAILCHIMP = os.environ.get('MAILCHIMP') or False
    MAILCHIMP_API = os.environ.get(
        'MAILCHIMP_API'
    ) or '*************************************'
    MAILCHIMP_LIST = os.environ.get('MAILCHIMP_LIST') or '52fb446a99'

    GOFAX_API_KEY = ''
    GOFAX_API_URL = ''

    HIDDEN_ITEMS = os.environ.get('HIDDEN_ITEMS') or ''
    SQLALCHEMY_ENGINE_OPTIONS = {"pool_pre_ping": True, }
    # https://github.com/flask-restful/flask-restful/issues/792
    PROPAGATE_EXCEPTIONS = True
    ADMIN_RAISE_ON_VIEW_EXCEPTION = True
    # scout config
    SCOUT_MONITOR = os.environ.get('SCOUT_MONITOR')
    SCOUT_KEY = os.environ.get('SCOUT_KEY')
    SCOUT_NAME = os.environ.get('SENTRY_ENVIRON')
    # If you'd like to utilize Error Monitoring:
    SCOUT_ERRORS_ENABLED = True

    PID_SECRET = os.environ.get('PID_SECRET')
    UTM_SECRET = os.environ.get('UTM_SECRET')
    DEFAULT_SCHEDULE_RUN_DAYS = os.environ.get('DEFAULT_SCHEDULE_RUN_DAYS')

    #fax
    GOFAX_API_KEY=os.environ.get('GOFAX_API_KEY')
    GOFAX_API_URL=os.environ.get('GOFAX_API_URL')