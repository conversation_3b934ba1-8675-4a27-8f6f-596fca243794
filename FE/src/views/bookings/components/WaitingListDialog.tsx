import {Dialog, Heading, Modal} from 'react-aria-components';

import {useMutation, useQuery} from '@apollo/client';
import {X} from 'lucide-react';

import {useDialogState} from '@/components/modal-state-provider.tsx';
import {Input, Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button.tsx';
import {Form, FormRootErrors} from '@/components/ui/form';
import {
  addPatientToWaitingList,
  getAllStudyRequest,
  getWaitingListData,
  mutateStudyRequestItem
} from '@/graphql/bookings.ts';
import {getUnitsList} from '@/graphql/lists.ts';
import {getFullName} from '@/lib/utils';

function WaitingListDialogue() {
  const [isOpen, setIsOpen, referral] = useDialogState('waiting-list-dialogue');
  const {data: unitsData} = useQuery(getUnitsList);
  const [addPatientToWaitingListMutation, {loading: isSubmitting}] = useMutation(addPatientToWaitingList, {
    refetchQueries: [getAllStudyRequest, getWaitingListData],
  });
  const [updateStudyRequestItem] = useMutation(mutateStudyRequestItem('status', 'accepted'));

  async function handleSubmit() {
    await addPatientToWaitingListMutation({
      variables: {
        waiting_list: {
          patientid: referral?.study_request?.pas_pt?.patientid,
          unit_id: referral?.procedure?.unit_id,
          study_request_item_id: referral?.id,
          referral_date: referral?.study_request?.date_recieved,
          provisional_procedure_date: referral?.provisional_procedure_date,
          procedure_ids: referral?.procedure_ids,
        },
      },
    });
  }

  return (
    <Modal
      isDismissable
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      className="react-aria-Modal w-110 rounded-md p-0"
    >
      <Dialog>
        <div className="flex items-center justify-between border-b border-neutral-200 px-6 py-4 text-neutral-800">
          <Heading
            className="text-sm font-semibold"
            slot="title"
          >
            Add to waiting list
          </Heading>
          <div onClick={() => setIsOpen(false)}>
            <X className="h-4 w-4" />
          </div>
        </div>

        <Form
          onSubmit={handleSubmit}
          onSubmitSuccess={async () => {
            await updateStudyRequestItem({
              variables: {
                id: referral?.id,
                value: 'accepted',
              },
            });
            setIsOpen(false);
          }}
        >
          <FormRootErrors />
          <div className="flex flex-col gap-y-4 p-6">
            <div className="grid grid-cols-2 gap-x-4 gap-y-4">
              <div className="flex flex-col gap-y-1">
                <div className="text-xs font-medium text-neutral-600">Patient name</div>
                <div className="text-xs text-neutral-800">
                  {getFullName(referral?.study_request?.pas_pt?.pas_pt_names?.[0])}
                </div>
              </div>
              <div className="flex flex-col gap-y-1">
                <div className="text-xs font-medium text-neutral-600">DOB</div>
                <div className="text-xs text-neutral-800">{referral?.study_request?.pas_pt?.dob}</div>
              </div>
              <div className="flex flex-col gap-y-1">
                <div className="text-xs font-medium text-neutral-600">Procedure</div>
                <div className="text-xs text-neutral-800">
                  {referral?.procedures
                    ?.map((p: any) => p.name)
                    .filter(Boolean)
                    .join(', ')}
                </div>
              </div>
              <div className="flex flex-col gap-y-1">
                <div className="text-xs font-medium text-neutral-600">Source</div>
                <div className="text-sm text-neutral-800">{referral?.source}</div>
              </div>
            </div>

            <div className="h-px w-full bg-neutral-200" />
            <div className="my-2 w-full">
              <Label className="text-xs text-neutral-700">Unit</Label>
              <Input
                className="react-aria-Input w-full cursor-not-allowed text-neutral-700 capitalize hover:border-neutral-400 focus:border-neutral-400 focus:ring-0"
                value={
                  unitsData?.list_units?.find((unit) => unit.id === referral?.procedure?.unit_id)
                    ?.description ?? ''
                }
                readOnly
              />
            </div>
          </div>

          <div className="flex items-center gap-x-3 border-t border-neutral-200 px-6 py-4">
            <Button
              onPress={() => setIsOpen(false)}
              variant="outlined"
              type="reset"
              className="react-aria-Button w-full rounded-sm"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="solid"
              className="react-aria-Button w-full rounded-sm"
              isDisabled={isSubmitting}
            >
              {isSubmitting ? 'Adding...' : 'Add to waiting list'}
            </Button>
          </div>
        </Form>
      </Dialog>
    </Modal>
  );
}

export default WaitingListDialogue;
