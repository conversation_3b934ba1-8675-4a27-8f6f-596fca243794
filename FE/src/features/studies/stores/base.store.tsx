import {RefObject} from 'react';

import {action, computed, makeObservable, observable, override, reaction, runInAction} from 'mobx';

import {PredResult} from '@/features/equations-registry/types.ts';
import {registerActionType, undoStack} from '@/features/undo-stack';
import type {Equation} from '@/store/equation.store.ts';
import {type ParamEquationMetadata, Parameter} from '@/store/parameter.ts';

export enum RefType {
  NO_REF,
  RANGE,
  ULN,
  LLN,
}

export class ParameterContext {
  constructor(
    public lln?: number | undefined,
    public uln?: number | undefined,
    public mpv?: number | undefined,
    public std?: number | undefined
  ) {}
}

export interface PredMetadataStore{
  predMetadata: Omit<ParamEquationMetadata, 'testid'>;
}

export interface DisposableStoreWithUpsert {
  upsertDB: (field?: string) => Promise<void>;
  moduleStore: PredMetadataStore;
  dispose?: () => void;
  disposers?: (() => void)[];
}

const zscoreFormatter = new Intl.NumberFormat('en-US', {
  maximumFractionDigits: 2,
  minimumFractionDigits: 2,
  signDisplay: 'always',
});

export class ParameterValue {
  public _result?: number;
  private proxyResult?: number;
  private _dbField?: string;
  predResult?: PredResult;
  predLoading?: boolean = true;
  predEquation?: Equation = undefined;
  elementRef?: RefObject<HTMLElement | null> = {current: null};

  constructor(
    public parameter: Parameter | undefined,
    public context: ParameterContext | null,
    public refType: RefType,
    protected store?: DisposableStoreWithUpsert,
    dbField?: string
  ) {
    makeObservable(this, {
      _result: observable,
      result: computed,
      refValue: computed,
      zScore: computed,
      setResult: action,
      predEquation: observable,
      predResult: observable,
      predLoading: observable,
      setProperty: action,
    });
    this._dbField = dbField;
    this.proxyResult = this._result;

    if (store?.disposers) {
      // NOTE: this function is prune to out of order execution.
      // perhaps a mobx flow or rxjs observable would be a better solution here
      const r1 = reaction(
        () => [this.store?.moduleStore?.predMetadata, this.parameter] as const,
        ([predMeta, param]) => {
          if (!predMeta || !param) return;
          this.setProperty('predLoading', true);
          param
            ?.getEquation({...predMeta, testid: this.parameter?.testid!})
            .then((equation) => {
              this.setProperty('predEquation', equation);
            })
            .finally(() => {
              this.setProperty('predLoading', false);
            });
        },
        {}
      );
      this.registerCalcPredReaction();
      this.store?.disposers?.push(r1);
    }
  }

  registerCalcPredReaction() {
    const r = reaction(
      () => [this.predEquation, this._result] as const,
      ([equation, result]) => {
        const predMeta = this.store?.moduleStore?.predMetadata;
        if (!equation || !predMeta) return;
        equation?.calcPred({...predMeta, testid: this.parameter?.testid!}, result).then((predResult) => {
          this.setProperty('predResult', predResult);
        });
      }
    );
    this.store?.disposers?.push(r);
  }

  setProperty<K extends keyof ParameterValue>(key: K, value: ParameterValue[K]) {
    this[key] = value as any;
  }

  get result(): number | undefined {
    const decimalPlaces = this.parameter?.decimalplaces;
    const value = Number(this._result?.toFixed(decimalPlaces));
    return !isNaN(value) ? value : undefined;
  }

  setResult(val: number | undefined, flash = false) {
    if (flash && this._result !== val) this.flash();
    this._result = val;
    this.proxyResult = this._result;
  }

  setProxyResult(val: number | undefined) {
    this.proxyResult = val;
  }

  reset() {
    this.proxyResult = this._result;
  }

  async commit() {
    if (this._result === this.proxyResult) return;
    undoStack.push({
      type: 'rft-parameter-value',
      payload: {
        paramValue: this,
        prevValue: this._result,
        newValue: this.proxyResult,
      },
    });

    this.setProperty('_result' as any, this.proxyResult);
    await this.upsertDB();
  }

  async upsertDB() {
    if (this.store && this._dbField) {
      await this.store.upsertDB(this._dbField);
    }
  }

  get predPercent() {
    if (!this.predResult) return undefined;
    const {mpv} = this.predResult;
    if (!mpv || !this.result) return undefined;
    return (this.result / mpv) * 100;
  }

  get refValue() {
    if (!this.predResult) return undefined;

    const {lln, uln, range} = this.predResult;
    if (!this.parameter) return undefined;

    if (this.refType === RefType.NO_REF) return undefined;
    if (this.refType === RefType.LLN) return `≥ ${lln?.toFixed(this.parameter.decimalplaces) ?? ''}`;
    if (this.refType === RefType.ULN) return `≤ ${uln?.toFixed(this.parameter.decimalplaces) ?? ''}`;
    if (this.refType === RefType.RANGE && range)
      return `${range[0]?.toFixed(this.parameter.decimalplaces) ?? ''} - ${range[1]?.toFixed(this.parameter.decimalplaces) ?? ''}`;
    if (this.refType === RefType.RANGE && !range)
      return `${lln?.toFixed(this.parameter.decimalplaces) ?? ''} - ${uln?.toFixed(this.parameter.decimalplaces) ?? ''}`;

    return undefined;
  }

  get zScore() {
    if (!this.predResult) return undefined;
    return this.predResult.zscore;
  }

  get zscoreFormatted() {
    const zScore = this.zScore;
    if (!zScore || isNaN(zScore)) return undefined;
    return zscoreFormatter.format(zScore);
  }

  scrollIntoView() {
    this.elementRef?.current?.scrollIntoView({behavior: 'smooth', block: 'nearest'});
  }

  flash() {
    const el = this.elementRef?.current;
    if (!el) return;

    el.classList.remove('flash-bg');
    void el.offsetWidth;
    el.classList.add('flash-bg');
  }
}

export class ComputedParameterValue extends ParameterValue {
  constructor(
    public valueFn: () => number | undefined,
    public parameter: Parameter | undefined,
    public context: ParameterContext,
    public refType: RefType,
    store?: DisposableStoreWithUpsert,
    dbField?: string,
    public autoCalculated: boolean = true
  ) {
    super(parameter, context, refType, store, dbField);
    makeObservable(this, {
      result: override,
    });

    reaction(() => this.result, (val) => {
      if (this.autoCalculated) {
        runInAction(() => {
          this._result = val;
        })
      }
    })
  }

  get result() {
    if (this.autoCalculated) {
      return this.valueFn();
    }
    return this._result;
  }

  calculate() {
    this._result = this.valueFn();
  }

  reset() {
    this._result = undefined;
  }
}

registerActionType('rft-parameter-value', {
  undo: ({paramValue, prevValue}: {paramValue: ParameterValue; prevValue: number | undefined}) => {
    paramValue.flash();
    paramValue.setResult(prevValue);
    paramValue.upsertDB();
    paramValue.scrollIntoView();
  },
  redo: ({paramValue, newValue}: {paramValue: ParameterValue; newValue: number | undefined}) => {
    paramValue.flash();
    paramValue.setResult(newValue);
    paramValue.upsertDB();
    paramValue.scrollIntoView();
  },
});
