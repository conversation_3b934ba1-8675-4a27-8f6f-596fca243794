@import './modal.css' layer(base);
@import './popover.css' layer(base);
@import './listbox.css' layer(base);
@import './select.css' layer(base);
@import './datepicker.css' layer(base);
@import './collapsible.css' layer(base);
@import './thin-table.css' layer(base);
@import './checkbox.css' layer(base);
@import './tooltip.css' layer(base);
@import './text-area.css' layer(base);
@import './taggroup.css' layer(base);
@import './grid-list.css' layer(base);
@import './menu.css' layer(base);
@import './switch.css' layer(base);
@import './combobox.css' layer(base);

@import 'tailwindcss';

@plugin "tailwindcss-animate";

@theme {
    --animate-collapsible-down: collapsible-down 0.2s ease-out;
    --animate-collapsible-up: collapsible-up 0.2s ease-out;
    --animate-flash-bg: flash 1s ease-in-out;

    @keyframes collapsible-down {
        from {
            height: 0;
        }
        to {
            height: 188px;
        }
    }

    @keyframes collapsible-up {
        from {
            height: 188px;
        }
        to {
            height: 0;
        }
    }

    @keyframes flash {
        0%, 100% {
            background-color: transparent;
        }
        10% {
            background-color: var(--color-amber-100);
        }
    }

    --font-sans: 'Figtree', sans-serif;

    --color-background: hsl(var(--background));
    --color-foreground: hsl(var(--foreground));

    --color-card: hsl(var(--card));
    --color-card-foreground: hsl(var(--card-foreground));

    --color-popover: hsl(var(--popover));
    --color-popover-foreground: hsl(var(--popover-foreground));

    --color-brand-50: #f4f9f8;
    --color-brand-100: #dcebe9;
    --color-brand-200: #b8d7d3;
    --color-brand-300: #8cbcb6;
    --color-brand-400: #5a8e8a;
    --color-brand-500: #4a827f;
    --color-brand-600: #3a6765;
    --color-brand-700: #315453;
    --color-brand-800: #2a4544;
    --color-brand-900: #263b3a;
    --color-brand-950: #122121;

    --color-brand2-50: #f4f8ed;
    --color-brand2-100: #e4efd8;
    --color-brand2-200: #cde0b6;
    --color-brand2-300: #b9d49c;
    --color-brand2-400: #8eb764;
    --color-brand2-500: #709b47;
    --color-brand2-600: #567b35;
    --color-brand2-700: #435f2c;
    --color-brand2-800: #384d27;
    --color-brand2-900: #324225;
    --color-brand2-950: #182310;

    --color-neutral-50: #fafafa;
    --color-neutral-100: #f7f7f7;
    --color-neutral-200: #ededed;
    --color-neutral-300: #dcdcdc;
    --color-neutral-400: #cbcbcb;
    --color-neutral-500: #b5b5b5;
    --color-neutral-600: #969696;
    --color-neutral-700: #777777;
    --color-neutral-800: #595959;
    --color-neutral-900: #404040;

    --color-chart-1: var(--color-brand-500);
    --color-chart-2: #E5961B;
    --color-chart-4: #274754;
    --color-chart-3: #e8c468;
    --color-chart-5: #f4a462;

    --color-blue-800: #5d6dba;
    --color-blue-500: #9db1dc;
    --color-blue-100: #f2f6fb;

    --color-discussion-50: #fcf9ee;
    --color-discussion-100: #c46f14;

    --color-completed-50: #f7fced;
    --color-completed-100: #598234;

    --color-unreported-50: #fef5f4;
    --color-unreported-100: #d43f44;

    --color-rezigrey-50: hsl(0deg 4.26% 91.72%);

    --color-primary: hsl(var(--primary));
    --color-primary-foreground: hsl(var(--primary-foreground));

    --color-secondary: hsl(var(--secondary));
    --color-secondary-foreground: hsl(var(--secondary-foreground));

    --color-muted: hsl(var(--muted));
    --color-muted-foreground: hsl(var(--muted-foreground));

    --color-accent: hsl(var(--accent));
    --color-accent-foreground: hsl(var(--accent-foreground));

    --color-destructive: hsl(var(--destructive));
    --color-destructive-foreground: hsl(var(--destructive-foreground));

    --color-border: hsl(var(--border));
    --color-input: hsl(var(--input));
    --color-ring: hsl(var(--ring));

    --color-sidebar-background: hsl(var(--sidebar-background));
    --color-sidebar-foreground: hsl(var(--sidebar-foreground));
    --color-sidebar-primary: hsl(var(--sidebar-primary));
    --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
    --color-sidebar-accent: hsl(var(--sidebar-accent));
    --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
    --color-sidebar-border: hsl(var(--sidebar-border));
    --color-sidebar-ring: hsl(var(--sidebar-ring));

    --leading-snug: 1.4;
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 175.38deg 22.41% 45.49%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --sidebar-background: 175 37% 56%;
        --sidebar-foreground: 240 5.3% 100%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --sidebar-background: 178 42% 33%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    body {
        @apply bg-background text-foreground;
    }

    table {
        thead > tr:first-child > th:first-child {
            border-top-left-radius: var(--radius-md);
        }

        thead > tr:first-child > th:last-child {
            border-top-right-radius: var(--radius-md);
        }
    }

    .ag-header-cell-menu-button {
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
    }

    .ag-header-cell:hover .ag-header-cell-menu-button {
        opacity: 1;
    }


    .ag-row-group .ag-cell {
        font-weight: 600;
        color: var(--color-neutral-800);
        background-color: var(--color-neutral-100);
        font-size: var(--text-xs);
        height: 34px;
    }

    .compact-ag-grid {
        .ag-root-wrapper {
            border: solid 1px;
            border-color: var(--color-neutral-300) !important;
        }

        .ag-row-group {
            background-color: var(--color-neutral-100);
            font-weight: 600;
            text-transform: uppercase;
            font-size: var(--text-xs);
        }

        .ag-cell {
            font-weight: 400;
            color: var(--color-neutral-700);
            font-size: var(--text-xs);
        }
    }

    .flash-bg {
        animation: flash 2.5s ease-in-out;
    }
}


.ag-referals {
    .ag-row-group .ag-cell {
        font-weight: 600;
        color: var(--color-neutral-800);
        background-color: var(--color-white);
        font-size: var(--text-xs);
        height: 38px;
        border-bottom: 1px solid var(--color-neutral-200);

    }

    .ag-cell-focus:focus-within {
        border-color: var(--color-brand2-500);
        outline: 1px;
    }

}

@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-border, currentColor);
    }
}
