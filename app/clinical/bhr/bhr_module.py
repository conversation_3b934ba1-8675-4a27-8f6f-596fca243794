import math
from typing import Iterable

from app.clinical.bhr.models import ProvTest
from app.clinical.models import RSession
from app.clinical.rft.rft_module import BaseTest
from app.clinical.types import ClinicalModule, TestParameter, RefType
from app.pdf_convert.models import PasPt
from app.normal_values.models import PredSourcexParameter, PredRefSources

class SpirometryTest(BaseTest):
    name = 'Spirometry'

    fev1 = TestParameter(param_name='FEV1', db_field='r_bl_fev1', ref_type=RefType.LLN)
    fvc = TestParameter(param_name='FVC', db_field='r_bl_fvc', ref_type=RefType.LLN)
    vc = TestParameter(param_name='VC', db_field='r_bl_vc', ref_type=RefType.LLN)
    fef2575 = TestParameter(param_name='FEF2575', db_field='r_bl_fef2575', ref_type=RefType.LLN)
    pef = TestParameter(param_name='PEF', db_field='r_bl_pef') 
    fer = TestParameter(param_name='FER', db_field='r_bl_fer', ref_type=RefType.LLN)


class BhrModule(ClinicalModule):
    _config_tests = [SpirometryTest()]

    def __init__(self, prov_test: ProvTest):
        self._prov_test = prov_test

        self.spirometry = SpirometryTest(self)

    def __getattr__(self, item):
        try:
            return super().__getattribute__(item)
        except AttributeError:
            pass

        if hasattr(self._prov_test, item):
            return getattr(self._prov_test, item)
        return super().__getattribute__(item)

    def get_test_session(self) -> 'RSession':
        return self._prov_test.session

    def get_patient(self) -> 'PasPt':
        return self._prov_test.patient

    def get_parameter_ids(self) -> Iterable[int]:
        return {pv.parameter.id for pv in self.spirometry.get_parameter_values()}

    def get_db_value(self, field_name: str) -> str | float | None:
        if hasattr(self._prov_test, field_name):
            return getattr(self._prov_test, field_name)
        return super().__getattribute__(field_name)

    def get_sources_by_test(self) -> dict:
        """
        Get sources for the BHR test by test type.
        """
        config = {
            'Spirometry': [self.spirometry]
        }

        sources_by_test = {}

        for test_name, tests in config.items():
            all_param_ids = set()

            for test in tests:
                for field_name, field_value in test._fields.items():
                    if isinstance(field_value, TestParameter) and field_value.parameter:
                        all_param_ids.add(field_value.parameter.id)

            if not all_param_ids:
                continue

            sourcex_records = PredSourcexParameter.query.filter(
                PredSourcexParameter.paramid.in_(list(all_param_ids))
            ).all()

            if sourcex_records:
                source_ids = [record.sourceid for record in sourcex_records]
                sources = PredRefSources.query.filter(
                    PredRefSources.id.in_(source_ids)
                ).all()

                unique_sources = list({source.source for source in sources if source.source})
                if unique_sources:
                    sources_by_test[test_name] = unique_sources

        return ', '.join(sources_by_test["Spirometry"]) if "Spirometry" in sources_by_test else ""
        

    def calc_pdx(self) -> str:
        """
        Calculate the provocative dose for a given percent fall.
        """
        protocol = self._prov_test.protocol
        if not protocol:
            return ""

        # Get threshold values
        thresh_fall = float(protocol.pd_threshold or "0")
        if thresh_fall == 0:
            return ""

        # Determine threshold level based on method reference
        method_reference = protocol.p_method_reference or ""

        # Get test data with dose schedule info
        test_data_with_schedule = []
        for td in self._prov_test.test_data:
            # Get the associated dose schedule
            dose_schedule = next((ds for ds in protocol.dose_schedules if ds.doseid == td.doseid), None)
            if dose_schedule:
                test_data_with_schedule.append({
                    'test_data': td,
                    'dose_schedule': dose_schedule,
                    'dose_number': int(dose_schedule.dose_number) if dose_schedule.dose_number and dose_schedule.dose_number.lstrip('-').isdigit() else None,
                    'dose_cumulative': dose_schedule.dose_cumulative,
                    'dose_discrete': dose_schedule.dose_discrete,
                    'response': td.response
                })

        # Sort by dose number
        test_data_with_schedule = sorted(
            test_data_with_schedule,
            key=lambda x: x['dose_number'] or 0
        )

        # Find reference dose for percentage calculation
        ref_data = None
        if method_reference == "Control":
            ref_data = next((td for td in test_data_with_schedule if td['dose_number'] == 1), None)
        elif method_reference == "Baseline":
            ref_data = next((td for td in test_data_with_schedule if td['dose_number'] == 0), None)
        
        if not ref_data or not self._is_numeric(ref_data['response']):
            return ""
        
        ref_response = float(ref_data['response'])
        thresh_level = 100.0 - thresh_fall  # Work with percentages

        # Filter valid doses (exclude baseline(-1), control(0), post-BD(100))
        valid_doses = []
        valid_responses = []
        dose_effect = protocol.p_dose_effect or ""

        for td in test_data_with_schedule:
            # Skip doses with None dose_number or excluded dose numbers
            if (td['dose_number'] is None or 
                td['dose_number'] in [-1, 0, 100] or
                not self._is_numeric(td['response']) or
                float(td['response']) <= 0):
                continue

            # Get dose value based on protocol preference, with fallback
            dose_value = None
            if dose_effect == "Cumulative":
                if td['dose_cumulative'] is not None and self._is_numeric(td['dose_cumulative']):
                    dose_value = float(td['dose_cumulative'])
                elif td['dose_discrete'] is not None and self._is_numeric(td['dose_discrete']):
                    dose_value = float(td['dose_discrete'])  # Fallback
            elif dose_effect == "Discrete":
                if td['dose_discrete'] is not None and self._is_numeric(td['dose_discrete']):
                    dose_value = float(td['dose_discrete'])
                elif td['dose_cumulative'] is not None and self._is_numeric(td['dose_cumulative']):
                    dose_value = float(td['dose_cumulative'])  # Fallback
            
            if dose_value is not None:
                # Calculate percentage response relative to reference
                response_pct = (100.0 * float(td['response'])) / ref_response
                valid_responses.append(response_pct)
                valid_doses.append(dose_value)

        if len(valid_doses) < 2:
            return ""

        # Find last valid dose
        dose_last_idx = len(valid_doses) - 1
        while dose_last_idx > 0 and not valid_responses[dose_last_idx]:
            dose_last_idx -= 1

        if dose_last_idx <= 0:
            return ""

        # Find doses that bracket the defined fall
        fall_achieved = False
        dose_after_idx = -1

        for i in range(len(valid_responses)):
            if valid_responses[i] == thresh_level:
                return f"{valid_doses[i]} {protocol.p_agent_units or ''}"
            elif valid_responses[i] < thresh_level:
                fall_achieved = True
                dose_after_idx = i
                break

        agent_units = protocol.p_agent_units or ""
        agent = protocol.p_agent or ""

        # If defined fall not achieved
        if not fall_achieved:
            # For Mannitol, check 10% change rule
            if agent.lower() == "mannitol":
                if (dose_last_idx > 0 and
                    valid_responses[dose_last_idx - 1] - valid_responses[dose_last_idx] >= 10):

                    # Extrapolate PD to threshold fall
                    extrapolated_dose = self._interpolate_dose(
                        valid_doses[dose_last_idx], valid_doses[dose_last_idx - 1],
                        valid_responses[dose_last_idx], valid_responses[dose_last_idx - 1],
                        thresh_level
                    )
                    decimal_places = protocol.pd_decimalplaces or 1
                    return f"{extrapolated_dose:.{decimal_places}f} {agent_units} (10% rule invoked)"
                else:
                    return f"> {valid_doses[dose_last_idx]} {agent_units}"
            else:
                return f"> {valid_doses[dose_last_idx]} {agent_units}"
        else:
            # Check if fall occurred after first dose
            if method_reference == "Control" and dose_after_idx == 0:
                return f"< {valid_doses[dose_after_idx]} {agent_units}"
            elif method_reference == "Baseline" and dose_after_idx == 0:
                return f"Response to control dose {agent_units}"

            # Interpolate PD
            if dose_after_idx > 0:
                interpolated_dose = self._interpolate_dose(
                    valid_doses[dose_after_idx], valid_doses[dose_after_idx - 1],
                    valid_responses[dose_after_idx], valid_responses[dose_after_idx - 1],
                    thresh_level
                )
                decimal_places = protocol.pd_decimalplaces or 1
                return f"{interpolated_dose:.{decimal_places}f} {agent_units}"

        return ""

    def _interpolate_dose(self, dose1: float, dose2: float, response1: float, response2: float, target_level: float) -> float:
        """
        Interpolate dose using logarithmic interpolation between two dose-response points.
        
        Args:
            dose1: First dose value
            dose2: Second dose value  
            response1: Response at first dose
            response2: Response at second dose
            target_level: Target response level to interpolate
        """
        a = math.log10(dose1)
        c = math.log10(dose2)
        b = response1
        d = response2
        m = (b - d) / (a - c)
        
        return 10 ** ((target_level - (b - m * a)) / m)

    def _is_numeric(self, value: str) -> bool:
        """Check if a string value can be converted to float."""
        if not value:
            return False
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False
        
    def get_test_data_by_doseid(self, doseid: int):
        """Get test data for a specific dose ID."""
        return next((td for td in self.test_data if td.doseid == doseid), None)

