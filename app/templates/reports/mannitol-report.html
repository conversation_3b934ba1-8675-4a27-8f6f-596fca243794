<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Mannitol Challenge Test Report</title>
  <meta name="pdfkit-page-size" content="A4" />
  <meta name="pdfkit-margin-top" content="0mm" />
  <meta name="pdfkit-margin-bottom" content="0mm" />
  <meta name="pdfkit-margin-left" content="0mm" />
  <meta name="pdfkit-margin-right" content="0mm" />
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap"
    rel="stylesheet" />
  {% include 'reports/shared/report-styles.html' %}

  <style>
    /* Mannitol Report Specific Styles */
    .medical-card {
      margin-bottom: 16px;
    }

    .clinical-notes {
      align-self: stretch;
      display: flex;
      font-size: 8px;
    }

    .test-sp {
      margin-top: 16px;
      width: 100%;
      display: flex;
      justify-content: flex-start;
    }

    .labels {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-end;
      width: 95px;
      font-size: 8px;
      font-weight: 500;
      margin-right: 6px;
    }

    .labels-heading {
      font-weight: bold;
      font-size: 8px;
      text-transform: uppercase;
      text-align: right;
      margin-bottom: 4px;
      height: 16px;
      line-height: 8px;
      display: flex;
      align-items: flex-end;
      color: #000000;
    }

    .labels-text {
      line-height: 12px;
      height: 12px;
      font-size: 6.5px;
      letter-spacing: -0.02em;
      text-align: right;
      color: #000000;
      white-space: nowrap;
    }

    .table-header {
      padding: 0 2px;
      border: 1px solid #DCDCDC;
      border-radius: 2px;
      height: 14px;
      width: 190px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .table-header-cell {
      color: #404040;
      font-weight: 600;
      font-size: 7px;
      line-height: 14px;
      letter-spacing: 0.02em;
      width: 100%;
      text-align: right;
      text-transform: uppercase;
      padding: 0 2px;
    }

    .baseline {
      font-style: italic;
      margin-top: 7px;
      margin-bottom: 4px;
      font-size: 8px;
      line-height: 8px;
      color: #000000;
    }

    .table-body {
      border: 1px solid #ededed;
      border-radius: 2px;
      font-size: 7.5px;
      font-weight: 500;
      color: #595959;
      width: 190px;
    }

    .table-row {
      display: flex;
      height: 12px;
      border-bottom: 1px solid #ededed;
      padding: 0 2px;
    }

    .table-row:last-child {
      border-bottom: 0;
    }

    .table-cell {
      width: 100%;
      line-height: 12px;
      text-align: right;
      color: #000000;
      padding: 0 2.75px;
    }

    .green-dot {
      height: 4.5px;
      width: 4.5px;
      background-color: #4A827F;
      border-radius: 50%;
      display: inline-block;
      margin-right: 2px;
    }

    .yellow-dot {
      height: 4.2px;
      width: 4.2px;
      background-color: #E5961B;
      border-radius: 10%;
      display: inline-block;
      margin-right: 2px;
    }

    .center {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .media {
      margin-top: 16px;
      margin-left: 12px;
      width: 100%;
      border: 1px solid #DCDCDC;
      height: 120px;
      border-radius: 4px;
    }

    .legend-text {
      font-size: 8px;
      font-weight: 500;
      line-height: 10px;
      color: #969696;
    }

    .horizontal {
      display: flex;
      align-items: center;
    }

    .chart {
      padding: 6px;
    }

    .z-score-scale {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 216px;
    }

    .z-score-scale-text {
      font-size: 7px;
      font-weight: 400;
      line-height: 10px;
      color: #595959;
    }

    .z-score-plot {
      margin-top: 4px;
    }

    .z-score-plot-container {
      height: 13px;
      width: 216px;
      z-index: 1;
      background-color: #FFE8E6;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 0 5px;
      margin-bottom: 2px;
    }

    .z-score-row {
      display: flex;
      justify-items: center;
    }

    .z-score-plot-lable {
      font-size: 7px;
      font-weight: 600;
      line-height: 10px;
      color: #595959;
      letter-spacing: 0.02em;
      text-transform: uppercase;
      margin-left: 5px;
    }

    .vertical-divider {
      height: 12px;
      width: 1px;
      border-left: 1px dashed #0000001F;
    }

    .main-container {
      display: flex;
      flex-wrap: wrap;
      margin-top: 16px;
      width: 100%;
      box-sizing: border-box;
    }

    .grid-item {
      display: flex;
      flex-direction: column;
      width: calc(50% - 12px);
      /* min-height: 200px; */
      box-sizing: border-box;
      align-items: flex-start;
      margin-right: 12px;
    }

    .grid-item:nth-child(2n) {
      margin-right: 0;
    }

    @media print {
      .main-container {
        display: flex !important;
        flex-wrap: wrap !important;
        width: 100% !important;
        margin-top: 16px !important;
      }

      .grid-item {
        width: calc(50% - 12px) !important;
        float: left !important;
        display: inline-block !important;
        vertical-align: top !important;
        break-inside: avoid !important;
        page-break-inside: avoid !important;
        min-height: auto !important;
        box-sizing: border-box !important;
        margin-right: 12px !important;
        margin-bottom: 30px !important;
      }

      .grid-item:nth-child(2n) {
        margin-right: 0 !important;
      }

      .grid-item:nth-child(2n+1) {
        clear: left !important;
      }

      .main-container::after {
        content: "";
        display: table;
        clear: both;
      }
    }

    .challenge-section {
      margin-top: 24px;
      width: 100%;
    }

    .challenge-title {
      font-size: 10px;
      font-weight: bold;
      letter-spacing: 0.05em;
      color: #000000;
      text-transform: uppercase;
      margin-bottom: 16px;
    }

    .challenge-tables {
      display: flex;
      gap: 24px;
      align-items: flex-start;
    }

    .table-header3 {
      padding: 0 2px;
      border: 1px solid #DCDCDC;
      border-radius: 2px;
      height: 14px;
      width: 240px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .table-body3 {
      border: 1px solid #ededed;
      border-radius: 2px;
      font-size: 7.5px;
      font-weight: 500;
      color: #595959;
      width: 240px;
    }

    .response-table {
      display: flex;
      flex-direction: column;
    }

    .response-title {
      font-size: 8px;
      font-weight: bold;
      text-transform: uppercase;
      color: #000000;
      margin-bottom: 4px;
    }

    .dose-response {
      margin-top: 8px;
      font-size: 8px;
      color: #000000;
    }

    .dose-label {
      font-weight: bold;
      text-transform: uppercase;
    }

    .dose-table-header {
      border: 1px solid #DCDCDC;
      border-radius: 2px;
      height: 14px;
      width: 100%;
      display: flex;
      font-size: 7px;
      font-weight: 600;
      color: #404040;
      margin-bottom: 2px;
      padding: 0 2px;
      align-items: center;
    }

    .dose-table-body {
      border: 1px solid #ededed;
      border-radius: 2px;
      font-size: 7.5px;
      color: #000000;
      width: 100%;
    }

    .dose-table-row {
      display: flex;
      height: 12px;
      border-bottom: 1px solid #ededed;
      padding: 0 2px;
      align-items: center;
    }

    .dose-table-row:last-child {
      border-bottom: 0;
    }

    .dose-table-cell {
      line-height: 12px;
      padding: 0 2px;
    }

    .dose-table-cell:first-child {
      width: 60px;
      text-align: left;
    }

    .dose-table-cell:nth-child(2) {
      width: 60px;
      text-align: right;
    }

    .dose-table-cell:last-child {
      width: 70px;
      text-align: right;
    }

    .media {
      margin-top: 4px;
      margin-left: 12px;
      width: 265px;
      border: 1px solid #DCDCDC;
      height: 100%;
      border-radius: 4px;
    }

    .legend-text {
      font-size: 8px;
      font-weight: 500;
      line-height: 10px;
      color: #969696;
    }

    .horizontal {
      display: flex;
      align-items: center;
    }

    .chart {
      padding: 6px;
    }

    .z-score-plot {
      margin-top: 4px;
    }

    .z-score-scale {
      position: relative;
      width: 216px;
      height: 10px;
    }

    .z-score-scale-text {
      position: absolute;
      font-size: 7px;
      font-weight: 400;
      line-height: 10px;
      color: #595959;
      transform: translateX(-50%);
    }

    .z-score-plot-container {
      height: 12px;
      width: 216px;
      z-index: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 0 5px;
      margin-bottom: 2px;
      position: relative;
    }

    .z-score-row {
      display: flex;
      justify-items: center;
      position: relative;
    }

    .z-score-plot-label {
      font-size: 7px;
      font-weight: 600;
      line-height: 10px;
      color: #595959;
      letter-spacing: 0.02em;
      text-transform: uppercase;
      margin-left: 5px;
    }

    .vertical-divider {
      height: 12px;
      width: 1px;
      border-left: 1px dashed #0000001F;
      position: absolute;
      top: 0;
    }

    .z-score-region {
      position: absolute;
      top: 0;
      height: 100%;
      z-index: 0;
    }

    .z-score-dot {
      position: absolute;
      top: 50%;
      width: 4.5px;
      height: 4.5px;
      border-radius: 50%;
      transform: translateX(-50%) translateY(-50%);
      z-index: 2;
    }

    .z-score-dot.bg-chart-2 {
      width: 4.2px;
      height: 4.2px;
      border-radius: 10%;
    }

    .bg-chart-1 {
      background-color: #4A827F;
    }

    .bg-chart-2 {
      background-color: #E5961B;
    }

    .report-text-full-width {
        margin-left: 0;
        padding-left: 6px;
        margin-top: 8px;
        border-left: 1.5px solid #8EB764;
      }

      .report-text-heading {
        font-size: 9px;
        font-weight: 600;
        line-height: 10px;
        color: #404040;
      }

      .report-reported-by {
        font-size: 8px;
        font-weight: 500;
        line-height: 10px;
        color: #404040;
        margin-top: 4px;
      }

      .normal-values-text {
        border-top: 1px solid #DCDCDC;
        margin-top: 6px;
        padding-top: 4px;
        font-size: 6.5px;
        font-weight: 400;
        line-height: 8.5px;
        color: #595959;
      }

      .page-footer {
        position: absolute;
        top: 810px;
        left: 16px;
        right: 16px;
      }
      .flow-vol {
        width: 120px;
        height: 120px;
        margin: 6px;
      }

      .report-text-body {
        font-size: 8px;
        font-weight: 400;
        line-height: 10px;
        color: #404040;
        margin-top: 4px;
      }

      .report-text {
        margin-left: 12px;
        padding-left: 6px;
        margin-top: 8px;
        border-left: 1.5px solid #8EB764;
      }
  </style>
</head>

<body>
  {% include 'reports/shared/report-header.html' %}

  {% set table_data = [] %}
  {% for dose_schedule in bhr.protocol.dose_schedules %}
    {% set test_data = bhr.get_test_data_by_doseid(dose_schedule.doseid) %}
    {% set table_row = {
      'label': dose_schedule.dose_xaxis_label,
      'units': bhr.protocol.p_agent_units if dose_schedule.dose_xaxis_label not in ['B/L', 'Post BD', 'Control'] else '',
      'result': test_data.result if test_data else '-',
      'response': test_data.response + '%' if test_data else '-'
    } %}
    {% if table_data.append(table_row) %}{% endif %}
  {% endfor %}
  

  <div class="test-info">
    <div>
      <div class="test-name">Mannitol Challenge Report</div>
      <div class="patient-name">{{bhr.patient.full_name}}</div>
    </div>

    <div class="left-section">
      <div class="text-small">
        <div class="row">
          <span class="light-gray text-small">Test Date:</span>{{ bhr.session.testdate }}
        </div>
        <div class="row" style="margin-top: 4px">
          <span class="light-gray text-small">Test Time:</span>{{ bhr.testtime }}
        </div>
      </div>
      <div class="text-small" style="min-width: 60px;">
        <div class="row">
          <span class="light-gray text-small">To:</span>{{ bhr.session.req_email or '' }}
        </div>
        <div class="row" style="margin-top: 4px">
          <span class="light-gray">CC:</span>{{ bhr.session.report_copyto or '' }}
        </div>
      </div>
    </div>
  </div>

  <div class="medical-card">
    <div class="main-info-row">
      <div class="info-column" style="min-width: 70px;">
        <div class="info-row">
          <div class="info-label">MRN:</div>
          <div class="info-value">{{ bhr.patient.ur }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">DOB:</div>
          <div class="info-value">{{ bhr.patient.dob }} ({{ "{:.2f}".format(bhr.session.age_at_test) }})</div>
        </div>
        <div class="info-row">
          <div class="info-label">Gender:</div>
          <div class="info-value">{{ 'Male' if bhr.patient.gender_forrfts_code == '1' else 'Female' }}</div>
        </div>
      </div>
      <div class="info-column" style="min-width: 70px;">
        <div class="info-row">
          <div class="info-label">Height:</div>
          <div class="info-value">{{ bhr.session.height or '' }} (cm)</div>
        </div>
        <div class="info-row">
          <div class="info-label">Weight:</div>
          <div class="info-value">{{ bhr.session.weight or '' }} (kg)</div>
        </div>
        <div class="info-row">
          <div class="info-label">BMI:</div>
          <div class="info-value">{{ bhr.session.bmi or '' }}</div>
        </div>
      </div>

      <div class="info-column" style="min-width: 70px;">
        <div class="info-row">
          <div class="info-label">Smoking Exposure:</div>
          <div class="info-value">{{ bhr.session.smoke_hx or '' }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">Pack years:</div>
          <div class="info-value">{{ bhr.session.smoke_packyears or '' }}</div>
        </div>
        <div class="info-row">
          <div class="info-label">Last Smoked:</div>
          <div class="info-value">{{ bhr.session.smoke_last or '' }}</div>
        </div>
      </div>
      <div class="info-column" style="min-width: 70px;">
        <div class="info-row">
          <div class="info-label">Last BD:</div>
          <div class="info-value">{{ bhr.bdstatus or '' }}</div>
        </div>
        <div class="address-row">
          <div class="info-label">Address:</div>
          <div class="info-value">{{ bhr.session.patient.addresses[0].address_1 or '' }}</div>
        </div>
      </div>
    </div>
    <div class="clinical-notes">
      <span class="notes-label">Clinical Notes: </span>
      <span class="notes-text">{{ bhr.session.req_clinicalnotes or '' }}</span>
    </div>
  </div>

  <div style="margin-top: 0px; margin-bottom: 0px;">
    <div style="font-size: 10px; font-weight: bold; letter-spacing: 0.05em; color: #000000; text-transform: uppercase;">
      {{bhr.testtype}}
    </div>
  </div>
    <div class="main-container">
      <div class="grid-item">
        <div class="test-sp">
          <div class="labels">
            <div class="labels-heading">Spirometry</div>
            {% for parameter in bhr.spirometry.get_parameter_values() %}
              <div class="labels-text">{{parameter.parameter.description}} [{{parameter.parameter.units_si}}]</div>
            {% endfor %}
          </div>
  
          <div>
            <div class="table-header">
              <div class="table-header-cell">Ref</div>
              <div class="table-header-cell">Result</div>
              <div class="table-header-cell">
                <div class="center"><span class="green-dot"></span>Z-Score</div>
              </div>
              <div class="table-header-cell">%pred</div>
            </div>
  
            <div class="table-body">
              {% for parameter in bhr.spirometry.get_parameter_values() %}
                <div class="table-row">
                  <div class="table-cell">{{parameter.ref_value}}</div>
                  <div class="table-cell">{{parameter.formated_value}}</div>
                  <div class="table-cell">{{parameter.zscore}}</div>
                  <div class="table-cell">{{parameter.pred_percentage}}</div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>

    <div class="grid-item">
      <div class="media">
        <div class="chart">
          <div class="horizontal">
            <div class="horizontal legend-text" style="margin-right: 6px">
              <span class="green-dot"></span>{{ z_score_plot.sp1_condition or 'Baseline' }}
            </div>
          </div>

          <div class="z-score-plot">
            <div class="z-score-scale">
              {% for tick in z_score_plot.axis_ticks %}
              <div class="z-score-scale-text" style="left: {{ " %.2f" | format(tick.position) }}%;">{{ tick.value }}
              </div>
              {% endfor %}
            </div>

            {% for parameter in z_score_plot.parameters %}
            <div class="z-score-row">
              <div class="z-score-plot-container">

                {% for region in parameter.regions %}
                <div class="z-score-region" style="left: {{ " %.2f" | format(region.start_pos) }}%; width: {{ "%.2f" |
                  format(region.width) }}%; background-color: {{ region.color }};"></div>
                {% endfor %}

                {% for tick in z_score_plot.axis_ticks %}
                <div class="vertical-divider" style="left: {{ " %.2f" | format(tick.position) }}%;"></div>
                {% endfor %}

                {% for dot in parameter.dots %}
                <div class="z-score-dot {{ dot.color_class }}" style="left: {{ " %.2f" | format(dot.position) }}%;"
                  title="{{ dot.color_class|replace('bg-chart-1', 'Baseline')|replace('bg-chart-2', 'Post-BD') }} Z-score: {{ dot.value }}">
                </div>
                {% endfor %}

              </div>
              <div class="z-score-plot-label">{{ parameter.description.lower() }}</div>
            </div>
            {% endfor %}

          </div>
        </div>
      </div>
    </div>

    <div class="grid-item">
      <div class="simple-response-table">
        <div class="response-title">Response</div>

        <div class="dose-table-body">
          <div class="dose-table-row">
            <div class="dose-table-cell">Level</div>
            <div class="dose-table-cell">FEV1</div>
            <div class="dose-table-cell">%Change</div>
          </div>
        </div>

        <div class="dose-table-body">
          {% for data in table_data %}
          <div class="dose-table-row">
            <div class="dose-table-cell">{{data.label}}{{data.units}}</div>
            <div class="dose-table-cell">{{data.result}}</div>
            <div class="dose-table-cell">{{data.response}}</div>
          </div>
          {% endfor %}
        </div>
      </div>
      <div>
        <div style="margin-top: 16px; font-size: 10px; font-weight: bold; letter-spacing: 0.05em; color: #000000; text-transform: uppercase;">
          Dose Response
        </div>
        <div style="font-size: 10px">
          PD{{bhr.protocol.pd_threshold}} = {{bhr.calc_pdx()}} Mannitol
        </div>
      </div>
    </div>


    <div class="grid-item">
      <div>
      <div class="dose-response-chart"
        style="padding: 0px;">
        <svg class="media" width="100%" height="100%" viewBox="0 0 320 240">
          <text x="160" y="20" text-anchor="middle" font-size="10" font-weight="bold" fill="#000">
            FEV1 (% of control)
          </text>

          <!-- Chart border -->
          <line x1="50" y1="35" x2="50" y2="175" stroke="#000" stroke-width="0.5" />
          <line x1="290" y1="35" x2="290" y2="175" stroke="#000" stroke-width="0.5" />
          <line x1="50" y1="175" x2="290" y2="175" stroke="#000" stroke-width="0.5" />


          <!-- Horizontal grid lines -->
          {% set y_min = bhr.protocol.plot_ymin|float %}
          {% set y_max = bhr.protocol.plot_ymax|float %}
          {% set y_step = bhr.protocol.plot_ystep|float %}
          {% set y_range = y_max - y_min %}

          <g stroke="#ccc" stroke-width="0.8">
            {% for i in range((y_range / y_step)|int + 1) %}
            {% set y_pos = 35 + (i * 140 / (y_range / y_step)) %}
            <line x1="75" y1="{{y_pos}}" x2="265" y2="{{y_pos}}" />
            {% endfor %}
          </g>

          <g stroke="#ccc" stroke-width="0.8">
            {% for i in range(1, table_data|length - 1) %}
            {% set x_pos = 50 + (i * 240 / (table_data|length - 1)) %}
            <line x1="{{x_pos}}" y1="35" x2="{{x_pos}}" y2="175" />
            {% endfor %}
          </g>

          <!-- Reference horizontal dashed line at pd value -->
          {% set pd_value = 100 - (bhr.protocol.pd_threshold|float) %}
          {% set ref_y = 175 - ((pd_value - y_min) / y_range * 140) %}
          <line x1="50" y1="{{ref_y}}" x2="290" y2="{{ref_y}}" stroke="#4A90E2" stroke-width="1.5"
            stroke-dasharray="5,5" />


          <!-- Y-axis labels -->
          <g font-size="10" fill="#333">
            {% for i in range((y_range / y_step)|int + 1) %}
            {% set y_pos = 35 + (i * 140 / (y_range / y_step)) + 4 %}
            {% set y_value = y_max - (i * y_step) %}
            <text x="45" y="{{y_pos}}" text-anchor="end">{{y_value|int}}</text>
            {% endfor %}
          </g>

          <!-- X-axis labels -->
          <g font-size="9" fill="#333">
            {% for i in range(table_data|length) %}
            {% set x_pos = 50 + (i * 240 / (table_data|length - 1)) %}
            <text x="{{x_pos}}" y="195" text-anchor="middle">{{table_data[i].label}}</text>
            {% endfor %}
          </g>

          <text x="170" y="220" text-anchor="middle" font-size="11" fill="#333">
            {{bhr.protocol.plot_xtitle or 'Log cumulative dose ({{{{bhr.protocol.p_agent_units}}}})'}}
          </text>

          <!-- Dashed line from B/L to first point -->
          {% if table_data|length > 1 and table_data[0].response and table_data[1].response %}
          {% set x1 = 50 %}
          {% set y1 = 175 - ((table_data[0].response|replace('%', '')|float - y_min) / y_range * 140) %}
          {% set x2 = 50 + (240 / (table_data|length - 1)) %}
          {% set y2 = 175 - ((table_data[1].response|replace('%', '')|float - y_min) / y_range * 140) %}
          <line x1="{{x1}}" y1="{{y1}}" x2="{{x2}}" y2="{{y2}}" stroke="#E53E3E" stroke-width="1"
            stroke-dasharray="4,2" />
          {% endif %}

          <!-- Solid line for middle points -->
          <polyline points="
              {% for i in range(1, table_data|length - 1) %}
                {% if table_data[i].response %}
                  {% set x_pos = 50 + (i * 240 / (table_data|length - 1)) %}
                  {% set response_val = table_data[i].response|replace('%', '')|float %}
                  {% set y_pos = 175 - ((response_val - y_min) / y_range * 140) %}
                  {{x_pos}},{{y_pos}}{% if not loop.last %} {% endif %}
                {% endif %}
              {% endfor %}
              " fill="none" stroke="#E53E3E" stroke-width="1" />

          <!-- Dashed line from last point to Post BD -->
          {% if table_data|length > 1 and table_data[-2].response and table_data[-1].response %}
          {% set x1 = 50 + ((table_data|length - 2) * 240 / (table_data|length - 1)) %}
          {% set y1 = 175 - ((table_data[-2].response|replace('%', '')|float - y_min) / y_range * 140) %}
          {% set x2 = 290 %}
          {% set y2 = 175 - ((table_data[-1].response|replace('%', '')|float - y_min) / y_range * 140) %}
          <line x1="{{x1}}" y1="{{y1}}" x2="{{x2}}" y2="{{y2}}" stroke="#E53E3E" stroke-width="1"
            stroke-dasharray="4,2" />
          {% endif %}

          <!-- Plot points -->
          <g fill="#E53E3E">
            {% for i in range(table_data|length) %}
            {% if table_data[i].response %}
            {% set x_pos = 50 + (i * 240 / (table_data|length - 1)) %}
            {% set response_val = table_data[i].response|replace('%', '')|float %}
            {% set y_pos = 175 - ((response_val - y_min) / y_range * 140) %}
            <circle cx="{{x_pos}}" cy="{{y_pos}}" r="3" />
            {% endif %}
            {% endfor %}
          </g>
        </svg>
      </div>
    </div>
    </div>
  </div>

  <div style="margin-top: 12px;">
    <div class="report-text-full-width">
      <div class="report-header-inline">
        <div class="report-text-body">
        <span class="report-text-heading"
          style="margin-right:2px">Technical Notes:</span> {{ bhr.technicalnotes or '-' }}
        </div>
      </div>
    </div>
    <div class="report-text-full-width">
        <div class="report-text-body">
          <div class="report-text-heading" style="margin-bottom:4px">Report</div>
          {{ bhr.report_text or '' }}
        </div>
        <div class="report-reported-by">
          <span style="margin-right:2px">Reported by:</span> {{ bhr.report_reportedby or '-' }}
          {% if bhr.report_reporteddate %} <span>on</span> {{ bhr.report_reporteddate }} {% endif %}
        </div>
        <div class="report-reported-by" style="margin-top:8px"> Signature: __________________________</div>
      </div>
  </div>

  <!-- Add footer for mannitol report -->
  {% set custom_footer_text = bhr.get_sources_by_test() %}
  {% include 'reports/shared/report-footer.html' %}
</body>

</html>