<!-- Common Report Styles Template Block -->
<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  @media print {
    body {
      zoom: 172%;
    }
  }

  body {
    font-family: Figtree, sans-serif;
    color: #333;
    background-color: white;
    padding: 16px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding-bottom: 4px;
  }

  .header img {
    height: 20px;
    width: auto;
  }

  .contact-info {
    display: flex;
    align-items: center;
  }

  .contact-info .contact-text {
    font-size: 8px;
    margin-left: 3px;
    white-space: nowrap;
    color: #777777;
  }

  .row {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }

  .contact-info svg {
    width: 10px;
    height: 10px;
    flex-shrink: 0;
  }

  .divider {
    width: 100%;
    height: 1px;
    background: #dcdcdc;
    margin-bottom: 8px;
  }

  .test-info {
    width: 100%;
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 8px;
  }

  .test-name {
    font-size: 10px;
    font-weight: 500;
    letter-spacing: 0.05em;
    color: #595959;
    text-transform: uppercase;
    margin-bottom: 5px;
  }

  .patient-name {
    font-size: 18px;
    font-weight: bold;
    letter-spacing: -0.02em;
    color: #000000;
    line-height: 20px;
  }

  .light-gray {
    color: #777777;
    font-size: 8px !important;
    line-height: 130%;
    margin-right: 4px;
  }

  .text-small {
    font-size: 8px;
    line-height: 130%;
  }

  .patient-info-section span {
    color: #595959;
  }

  .left-section {
    display: flex;
    gap: 10px;
    width: max-content;
  }

  .medical-card {
    width: 100%;
    padding: 8px 10px;
    background: #F7F7F7;
    overflow: hidden;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .main-info-row {
    align-self: stretch;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 6px;
  }

  .info-column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
  }

  .info-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 5px;
  }

  .info-row:last-child {
    margin-bottom: 0;
  }

  .info-label {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    color: #595959;
    font-size: 8px;
    font-weight: 400;
    line-height: 10.40px;
    word-wrap: break-word;
  }

  .info-value {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    color: #404040;
    font-size: 8px;
    font-weight: 400;
    line-height: 10.40px;
    margin-left: 2px;
    word-wrap: break-word;
  }

  .address-row {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 2px;
    margin-bottom: 3px;
  }

  .clinical-notes {
    font-size: 8px;
  }

  .notes-label {
    color: #595959;
    font-size: 8px;
    font-weight: 400;
    line-height: 10px;
    white-space: nowrap;
  }

  .notes-text {
    color: #404040;
    font-size: 8px;
    font-weight: 400;
    line-height: 10px;
    word-wrap: break-word;
  }

  .page-footer {
    position: absolute;
    top: 810px;
    left: 16px;
    right: 16px;
  }

  .normal-values-text {
    border-top: 1px solid #DCDCDC;
    margin-top: 6px;
    padding-top: 4px;
    font-size: 6.5px;
    font-weight: 400;
    line-height: 8.5px;
    color: #595959;
  }

  .green-dot {
    height: 4.5px;
    width: 4.5px;
    background-color: #4A827F;
    border-radius: 999999999px;
    display: inline-block;
    margin-right: 2px;
    flex-shrink: 0;
  }

  .yellow-dot {
    height: 4.2px;
    width: 4.2px;
    background-color: #E5961B;
    border-radius: 10%;
    display: inline-block;
    margin-right: 2px;
    flex-shrink: 0;
  }

  .center {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
</style>