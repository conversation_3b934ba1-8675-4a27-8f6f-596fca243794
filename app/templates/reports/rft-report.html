<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Respiratory Function Test Report</title>
    <meta name="pdfkit-page-size" content="A4" />
    <meta name="pdfkit-margin-top" content="0mm" />
    <meta name="pdfkit-margin-bottom" content="0mm" />
    <meta name="pdfkit-margin-left" content="0mm" />
    <meta name="pdfkit-margin-right" content="0mm" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap"
      rel="stylesheet"
    />
    {% include 'reports/shared/report-styles.html' %}
    <style>
      /* RFT Specific Additional Styles */


      .test-sp {
        margin-top: 8px;
        width: 100%;
        display: flex;
        justify-content: flex-start;
      }

      .labels {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-end;
        width: 95px;
        font-size: 8px;
        font-weight: 500;
        margin-right: 6px;
      }

      .labels-heading {
        font-weight: bold;
        font-size: 8px;
        text-transform: uppercase;
        text-align: right;
        margin-bottom: 4px;
        height: 16px;
        line-height: 8px;
        display: flex;
        align-items: flex-end;
        color: #000000;
      }

      .labels-text {
        line-height: 12px;
        height: 12px;
        font-size: 6.5px;
        letter-spacing: -0.02em;
        text-transform: uppercase;
        text-align: right;
        color: #000000;
        white-space: nowrap;
      }

      .table-header {
        padding: 0 2px;
        border: 1px solid #DCDCDC;
        border-radius: 2px;
        height: 14px;
        width: 190px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }

      .table-header-cell {
        color: #404040;
        font-weight: 600;
        font-size: 7px;
        line-height: 14px;
        letter-spacing: 0.02em;
        width: 100%;
        text-align: right;
        text-transform: uppercase;
        padding: 0 2px;
      }

      .baseline {
        font-style: italic;
        margin-top: 7px;
        margin-bottom: 4px;
        font-size: 8px;
        line-height: 8px;
        color: #000000;
      }

      .table-body {
        border: 1px solid #ededed;
        border-radius: 2px;
        font-size: 7.5px;
        font-weight: 500;
        color: #595959;
        width: 190px;
      }

      .table-header2 {
        padding: 0 2px;
        border: 1px solid #DCDCDC;
        border-radius: 2px;
        height: 14px;
        width: 265px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }

      .table-body2 {
        border: 1px solid #ededed;
        border-radius: 2px;
        font-size: 7.5px;
        font-weight: 500;
        color: #595959;
        width: 265px;
      }

      .table-row {
        display: flex;
        height: 12px;
        border-bottom: 1px solid #ededed;
        padding: 0 2px;
      }

      .table-row:last-child {
        border-bottom: 0;
      }

      .table-cell {
        width: 100%;
        line-height: 12px;
        text-align: right;
        color: #000000;
        padding: 0 2.75px;
      }

      .left-section {
        display: flex;
        gap: 10px;
        width: max-content;
      }

      .medical-card {
        width: 100%;
        padding: 8px 10px;
        background: #F7F7F7;
        overflow: hidden;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
      }

      .main-info-row {
        align-self: stretch;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 6px;
      }

      .info-column {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
      }

      .info-row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-bottom: 5px;
      }

      .info-row:last-child {
        margin-bottom: 0;
      }

      .info-label {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        color: #595959;
        font-size: 8px;
        font-weight: 400;
        line-height: 10.40px;
        word-wrap: break-word;
      }

      .info-value {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        color: #404040;
        font-size: 8px;
        font-weight: 400;
        line-height: 10.40px;
        margin-left: 2px;
        word-wrap: break-word;
      }

      .address-row {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        gap: 2px;
        margin-bottom: 3px;
      }

      .clinical-notes {
        font-size: 8px;
      }

      .notes-label {
        color: #595959;
        font-size: 8px;
        font-weight: 400;
        line-height: 10px;
        white-space: nowrap;
      }

      .notes-text {
        color: #404040;
        font-size: 8px;
        font-weight: 400;
        line-height: 10px;
        word-wrap: break-word;
      }


      .media {
        margin-top: 4px;
        margin-left: 12px;
        width: 265px;
        border: 1px solid #DCDCDC;
        height: 100%;
        border-radius: 4px;
      }

      .legend-text {
        font-size: 8px;
        font-weight: 500;
        line-height: 10px;
        color: #969696;
      }

      .horizontal {
        display: flex;
        align-items: center;
      }

      .chart {
        padding: 6px;
      }

      .z-score-plot {
        margin-top: 4px;
      }

      .z-score-scale {
        position: relative;
        width: 216px;
        height: 10px;
      }

      .z-score-scale-text {
        position: absolute;
        font-size: 7px;
        font-weight: 400;
        line-height: 10px;
        color: #595959;
        transform: translateX(-50%);
      }

      .z-score-plot-container {
        height: 12px;
        width: 216px;
        z-index: 1;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 5px;
        margin-bottom: 2px;
        position: relative;
      }

      .z-score-row {
        display: flex;
        justify-items: center;
        position: relative;
      }

      .z-score-plot-label {
        font-size: 7px;
        font-weight: 600;
        line-height: 10px;
        color: #595959;
        letter-spacing: 0.02em;
        text-transform: uppercase;
        margin-left: 5px;
      }

      .vertical-divider {
        height: 12px;
        width: 1px;
        border-left: 1px dashed #0000001F;
        position: absolute;
        top: 0;
      }

      .z-score-region {
        position: absolute;
        top: 0;
        height: 100%;
        z-index: 0;
      }

      .z-score-dot {
        position: absolute;
        top: 50%;
        width: 4.5px;
        height: 4.5px;
        border-radius: 50%;
        transform: translateX(-50%) translateY(-50%);
        z-index: 2;
      }

      .z-score-dot.bg-chart-2 {
        width: 4.2px;
        height: 4.2px;
        border-radius: 10%;
      }

      .bg-chart-1 {
        background-color: #4A827F;
      }

      .bg-chart-2 {
        background-color: #E5961B;
      }

      .flow-vol {
        width: 120px;
        height: 120px;
        margin: 6px;
      }

      .report-text-body {
        font-size: 8px;
        font-weight: 400;
        line-height: 10px;
        color: #404040;
        margin-top: 4px;
      }

      .report-text {
        margin-left: 12px;
        padding-left: 6px;
        margin-top: 8px;
        border-left: 1.5px solid #8EB764;
      }

      .report-text-full-width {
        margin-left: 0;
        padding-left: 6px;
        margin-top: 8px;
        border-left: 1.5px solid #8EB764;
      }

      .report-text-heading {
        font-size: 9px;
        font-weight: 600;
        line-height: 10px;
        color: #404040;
      }

      .report-reported-by {
        font-size: 8px;
        font-weight: 500;
        line-height: 10px;
        color: #404040;
        margin-top: 4px;
      }

      .normal-values-text {
        border-top: 1px solid #DCDCDC;
        margin-top: 6px;
        padding-top: 4px;
        font-size: 6.5px;
        font-weight: 400;
        line-height: 8.5px;
        color: #595959;
      }

      .page-footer {
        position: absolute;
        top: 810px;
        left: 16px;
        right: 16px;
      }

    </style>

  </head>
  <body>
    <div class="content">
      {% include 'reports/shared/report-header.html' %}

      <div class="test-info">
        <div>
          <div class="test-name">Respiratory Function Report</div>
          <div class="patient-name">{{ rft.patient.full_name }}</div>
        </div>

        <div class="left-section">
          <div class="text-small">
            <div class="row"><span class="light-gray text-small">Test Date:</span>{{ rft.session.testdate }}</div>
            <div class="row" style="margin-top: 4px"><span
              class="light-gray text-small">Test Time:</span>{{ rft.testtime }}
            </div>
          </div>
          <div class="text-small" style="min-width: 60px;">
            <div class="row"><span class="light-gray text-small">To:</span>{{ rft.session.req_email or '' }}</div>
            <div class="row" style="margin-top: 4px"><span
              class="light-gray">CC:</span>{{ rft.session.report_copyto or '' }}
            </div>
          </div>
        </div>
      </div>

      <div class="medical-card">
        <div class="main-info-row">
          <div class="info-column" style="min-width: 70px;">
            <div class="info-row">
              <div class="info-label">MRN:</div>
              <div class="info-value">{{ rft.patient.ur }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">DOB:</div>
              <div class="info-value">{{ rft.patient.dob }} ({{ "{:.2f}".format(rft.session.age_at_test) }})</div>
            </div>
            <div class="info-row">
              <div class="info-label">Gender:</div>
              <div class="info-value">{{ 'Male' if rft.patient.gender_forrfts_code == '1' else 'Female' }}</div>
            </div>
          </div>
          <div class="info-column" style="min-width: 70px;">
            <div class="info-row">
              <div class="info-label">Height:</div>
              <div class="info-value">{{ rft.session.height or '' }} (cm)</div>
            </div>
            <div class="info-row">
              <div class="info-label">Weight:</div>
              <div class="info-value">{{ rft.session.weight or '' }} (kg)</div>
            </div>
            <div class="info-row">
              <div class="info-label">BMI:</div>
              <div class="info-value">{{ rft.session.bmi or '' }}</div>
            </div>
          </div>

          <div class="info-column" style="min-width: 70px;">
            <div class="info-row">
              <div class="info-label">Smoking Exposure:</div>
              <div class="info-value">{{ rft.session.smoke_hx or '' }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Pack years:</div>
              <div class="info-value">{{ rft.session.smoke_packyears or '' }}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Last Smoked:</div>
              <div class="info-value">{{ rft.session.smoke_last or '' }}</div>
            </div>
          </div>
          <div class="info-column" style="min-width: 70px;">
            <div class="info-row">
              <div class="info-label">Last BD:</div>
              <div class="info-value">{{ rft.bdstatus or '' }}</div>
            </div>
            <div class="address-row">
              <div class="info-label">Address:</div>
              <div class="info-value">{{ rft.session.patient.addresses[0].address_1 or '' }}</div>
            </div>
          </div>
        </div>
        <div class="clinical-notes">
          <span class="notes-label">Clinical Notes: </span>
          <span class="notes-text">{{ rft.session.req_clinicalnotes or '' }}</span>
        </div>
      </div>


      {% if not rft.sp1.is_empty() or not rft.sp2.is_empty() %}
        <div style="display: flex">

          <div class="test-sp" style="margin-right: 12px">
            <div class="labels">
              <div class="labels-heading">Spirometry</div>
              <div class="labels-text">fev1 (L)</div>
              <div class="labels-text">fvc (L)</div>
              <div class="labels-text">vc (L)</div>
              <div class="labels-text">fev1/fvc (%)</div>
              <div class="labels-text">fev1/vc (%)</div>
              <div class="labels-text">fef25-75 (L/sec)</div>
              <div class="labels-text">pef (L/sec)</div>
            </div>

            <div>
              <div class="table-header">
                <div class="table-header-cell">Ref</div>
                <div class="table-header-cell">Result</div>
                <div class="table-header-cell">
                  <div class="center"><span class="green-dot"></span>Z-Score</div>
                </div>
                <div class="table-header-cell">%pred</div>
              </div>

              <div class="baseline">{{ rft.sp1.condition.value or "Baseline" }}</div>

              <div class="table-body">
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp1.fev1.ref_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1.zscore }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1.pred_percentage }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp1.fvc.ref_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fvc.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fvc.zscore }}</div>
                  <div class="table-cell">{{ rft.sp1.fvc.pred_percentage }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp1.vc.ref_value }}</div>
                  <div class="table-cell">{{ rft.sp1.vc.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp1.vc.zscore }}</div>
                  <div class="table-cell">{{ rft.sp1.vc.pred_percentage }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp1.fev1_fvc.ref_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1_fvc.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1_fvc.zscore }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1_fvc.pred_percentage }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp1.fev1_fvc.ref_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1_vc.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1_fvc.zscore }}</div>
                  <div class="table-cell">{{ rft.sp1.fev1_fvc.pred_percentage }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp1.fef2575.ref_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fef2575.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp1.fef2575.zscore }}</div>
                  <div class="table-cell">{{ rft.sp1.fef2575.pred_percentage }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp1.pef.ref_value }}</div>
                  <div class="table-cell">{{ rft.sp1.pef.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp1.pef.zscore }}</div>
                  <div class="table-cell">{{ rft.sp1.pef.pred_percentage }}</div>
                </div>

              </div>
            </div>
          </div>

          <div class="test-sp">

            <div>
              <div class="table-header2">
                <div class="table-header-cell">Result</div>
                <div class="table-header-cell">
                  <div class="center"><span class="yellow-dot"></span>Z-Score</div>
                </div>
                <div class="table-header-cell">%pred</div>
                <div class="table-header-cell">Change</div>
                <div class="table-header-cell">% CHG</div>
              </div>

              <div class="baseline">{{ rft.sp2.condition.value or 'Post BD (Salb MDI)' }}</div>

              <div class="table-body2">
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp2.fev1.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1.zscore }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1.pred_percentage }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1_change }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1_percent_change }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp2.fvc.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp2.fvc.zscore }}</div>
                  <div class="table-cell">{{ rft.sp2.fvc.pred_percentage }}</div>
                  <div class="table-cell">{{ rft.sp2.fvc_change }}</div>
                  <div class="table-cell">{{ rft.sp2.fvc_percent_change }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp2.vc.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp2.vc.zscore }}</div>
                  <div class="table-cell">{{ rft.sp2.vc.pred_percentage }}</div>
                  <div class="table-cell">{{ rft.sp2.vc_change }}</div>
                  <div class="table-cell">{{ rft.sp2.vc_percent_change }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp2.fev1_fvc.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1_fvc.zscore }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1_fvc.pred_percentage }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1_fvc_change }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1_fvc_percent_change }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp2.fev1_vc.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1_fvc.zscore }}</div>
                  <div class="table-cell">{{ rft.sp2.fev1_fvc.pred_percentage }}</div>
                  <div class="table-cell">-</div>
                  <div class="table-cell">-</div>
                </div>

                <div class="table-row">
                  <div class="table-cell">{{ rft.sp2.fef2575.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp2.fef2575.zscore }}</div>
                  <div class="table-cell">{{ rft.sp2.fef2575.pred_percentage }}</div>
                  <div class="table-cell">{{ rft.sp2.fef2575_change }}</div>
                  <div class="table-cell">{{ rft.sp2.fef2575_percent_change }}</div>
                </div>
                <div class="table-row">
                  <div class="table-cell">{{ rft.sp2.pef.formated_value }}</div>
                  <div class="table-cell">{{ rft.sp2.pef.zscore }}</div>
                  <div class="table-cell">{{ rft.sp2.pef.pred_percentage }}</div>
                  <div class="table-cell">{{ rft.sp2.pef_change }}</div>
                  <div class="table-cell">{{ rft.sp2.pef_percent_change }}</div>

                </div>

              </div>
            </div>
          </div>
        </div>
      {% endif %}
      {% if not rft.blood_gasses.is_empty() %}
        <div style="display: flex">
          <div>
            {% if not rft.co_transfer.is_empty() %}
              <div class="test-sp">
                <div class="labels">
                  <div class="labels-heading">Co transfer factor</div>
                  <div class="labels-text">tlco (ml/min/mmHg)</div>
                  <div class="labels-text">tlcoHb (ml/min/mmHg)</div>
                  <div class="labels-text">va (L)</div>
                  <div class="labels-text">Kco (ml/min/mmHg/L)</div>
                  <div class="labels-text">KcoHb (ml/min/mmHg/L)</div>
                  <div class="labels-text">Vi (l)</div>
                  <div class="labels-text">Hb (gm/dl)</div>
                </div>

                <div>
                  <div class="baseline">{{ rft.co_transfer.condition.value or 'Baseline' }}</div>
                  <div class="table-body">
                    <div class="table-row">
                      <div class="table-cell">{{ rft.co_transfer.tlco.ref_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.tlco.formated_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.tlco.zscore }}</div>
                      <div class="table-cell">{{ rft.co_transfer.tlco.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.co_transfer.tlco.ref_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.tlco_hb }}</div>
                      <div class="table-cell">{{ rft.co_transfer.tlco_hb.zscore }}</div>
                      <div class="table-cell">{{ rft.co_transfer.tlco_hb.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.co_transfer.va.ref_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.va.formated_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.va.zscore }}</div>
                      <div class="table-cell">{{ rft.co_transfer.va.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.co_transfer.kco.ref_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.kco.formated_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.kco.zscore }}</div>
                      <div class="table-cell">{{ rft.co_transfer.kco.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.co_transfer.kco.ref_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.kco_hb }}</div>
                      <div class="table-cell">{{ rft.co_transfer.kco_hb.zscore }}</div>
                      <div class="table-cell">{{ rft.co_transfer.kco_hb.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.co_transfer.vi.ref_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.vi.formated_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.vi.zscore }}</div>
                      <div class="table-cell">{{ rft.co_transfer.vi.pred_percentage }}</div>
                    </div>

                    <div class="table-row">
                      <div class="table-cell">{{ rft.co_transfer.hb.ref_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.hb.formated_value }}</div>
                      <div class="table-cell">{{ rft.co_transfer.hb.zscore }}</div>
                      <div class="table-cell">{{ rft.co_transfer.hb.pred_percentage }}</div>
                    </div>

                  </div>
                </div>
              </div>
            {% endif %}
            {% if not rft.lung_volumes.is_empty() %}
              <div class="test-sp">
                <div class="labels">
                  <div class="labels-heading">Lung volumes</div>
                  <div class="labels-text">tlc (l)</div>
                  <div class="labels-text">lvvc (l)</div>
                  <div class="labels-text">ic (L)</div>
                  <div class="labels-text">frc (l)</div>
                  <div class="labels-text">erv (l)</div>
                  <div class="labels-text">rv (l)</div>
                  <div class="labels-text">rv/tlc (%)</div>
                </div>

                <div>
                  <div class="baseline">{{ rft.lung_volumes.condition.value or 'Baseline' }}
                    ({{ rft.lung_volumes.method.value or '' }})
                  </div>

                  <div class="table-body">
                    <div class="table-row">
                      <div class="table-cell">{{ rft.lung_volumes.tlc.ref_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.tlc.formated_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.tlc.zscore }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.tlc.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.lung_volumes.lvvc.ref_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.lvvc.formated_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.lvvc.zscore }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.lvvc.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.lung_volumes.ic.ref_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.ic.formated_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.ic.zscore }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.ic.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.lung_volumes.frc.ref_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.frc.formated_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.frc.zscore }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.frc.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.lung_volumes.erv.ref_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.erv.formated_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.erv.zscore }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.erv.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.lung_volumes.rv.ref_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.rv.formated_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.rv.zscore }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.rv.pred_percentage }}</div>
                    </div>

                    <div class="table-row">
                      <div class="table-cell">{{ rft.lung_volumes.rv_tlc.ref_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.rv_tlc.formated_value }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.rv_tlc.zscore }}</div>
                      <div class="table-cell">{{ rft.lung_volumes.rv_tlc.pred_percentage }}</div>
                    </div>
                  </div>
                </div>
              </div>
            {% endif %}
            {% if not rft.feno.is_empty() %}
              <div class="test-sp">
                <div class="labels">
                  <div class="labels-heading">exhaled nitric oxide</div>
                  <div class="labels-text">feno (ppb)</div>
                </div>

                <div>
                  <div class="baseline">{{ rft.feno.condition.value or 'Baseline' }}</div>

                  <div class="table-body">
                    <div class="table-row">
                      <div class="table-cell">{{ rft.feno.feno.ref_value }}</div>
                      <div class="table-cell">{{ rft.feno.feno.formated_value }}</div>
                      <div class="table-cell">{{ rft.feno.feno.zscore }}</div>
                      <div class="table-cell">{{ rft.feno.feno.pred_percentage }}</div>
                    </div>
                  </div>
                </div>
              </div>
            {% endif %}
            {% if not rft.mrps.is_empty() %}
              <div class="test-sp">
                <div class="labels">
                  <div class="labels-heading">MRPS</div>
                  <div class="labels-text">mip (cmh2O)</div>
                  <div class="labels-text">mep (cmh2O)</div>
                  <div class="labels-text">snip (cmh2O)</div>
                </div>

                <div>
                  <div class="baseline">{{ rft.mrps.condition.value or 'Baseline' }}</div>

                  <div class="table-body">
                    <div class="table-row">
                      <div class="table-cell">{{ rft.mrps.mip.ref_value }}</div>
                      <div class="table-cell">{{ rft.mrps.mip.formated_value }}</div>
                      <div class="table-cell">{{ rft.mrps.mip.zscore }}</div>
                      <div class="table-cell">{{ rft.mrps.mip.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.mrps.mep.ref_value }}</div>
                      <div class="table-cell">{{ rft.mrps.mep.formated_value }}</div>
                      <div class="table-cell">{{ rft.mrps.mep.zscore }}</div>
                      <div class="table-cell">{{ rft.mrps.mep.pred_percentage }}</div>
                    </div>
                    <div class="table-row">
                      <div class="table-cell">{{ rft.mrps.snip.ref_value }}</div>
                      <div class="table-cell">{{ rft.mrps.snip.formated_value }}</div>
                      <div class="table-cell">{{ rft.mrps.snip.zscore }}</div>
                      <div class="table-cell">{{ rft.mrps.snip.pred_percentage }}</div>
                    </div>
                  </div>
                </div>
              </div>
            {% endif %}
            <div class="test-sp">
              <div class="labels">
                <div class="labels-heading">Blood gases</div>
                <div class="labels-text">Sample type</div>
                <div class="labels-text">fio2</div>
                <div class="labels-text">ph</div>
                <div class="labels-text">PaCO2 (mmHg)</div>
                <div class="labels-text">PaO2 (mmHg)</div>
                <div class="labels-text">HCO3 (mmol/L)</div>
                <div class="labels-text">BE (mmol/L)</div>
                <div class="labels-text">SaO2 (%)</div>
                <div class="labels-text">SpO2 (%)</div>
                <div class="labels-text">COHb (%)</div>
                <div class="labels-text">A-aPO2 (mmHg)</div>
                <div class="labels-text">Shunt-anatomic (%)</div>
              </div>

              <div>
                <div class="table-header" style="margin-bottom: 4px">
                  <div class="table-header-cell">Ref</div>
                  <div class="table-header-cell">Result 1</div>
                  <div class="table-header-cell">Result 2</div>
                </div>

                <div class="table-body">
                  <div class="table-row">
                    <div class="table-cell">-</div>
                    <div class="table-cell">{{ rft.blood_gasses.sample_type_1.value or '-' }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.sample_type_2.value or '-' }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">-</div>
                    <div class="table-cell">{{ rft.blood_gasses.fio1.value or '-' }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.fio2.value or '-' }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.ph1.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.ph1.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.ph2.formated_value }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.paco21.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.paco21.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.paco22.formated_value }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.pao21.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.pao21.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.pao22.formated_value }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.hco31.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.hco31.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.hco32.formated_value }}</div>
                  </div>

                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.be1.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.be1.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.be2.formated_value }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.sao21.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.sao21.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.sao22.formated_value }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.spo21.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.spo21.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.spo22.formated_value }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.cohb1.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.cohb1.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.cohb2.formated_value }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.aapo21.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.aapo21.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.aapo22.formated_value }}</div>
                  </div>
                  <div class="table-row">
                    <div class="table-cell">{{ rft.blood_gasses.shunt1.ref_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.shunt1.formated_value }}</div>
                    <div class="table-cell">{{ rft.blood_gasses.shunt2.formated_value }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div>
            <div class="media">
              <div class="chart">
                <!-- Legend -->
                <div class="horizontal">
                  <div class="horizontal legend-text" style="margin-right: 6px">
                    <span class="green-dot"></span>{{ z_score_plot.sp1_condition or 'Baseline' }}
                  </div>
                  <div class="horizontal legend-text">
                    <span class="yellow-dot"></span>{{ z_score_plot.sp2_condition or 'Post BD (Salb MDI)' }}
                  </div>
                </div>

                <div class="z-score-plot">
                  <div class="z-score-scale">
                    {% for tick in z_score_plot.axis_ticks %}
                      <div class="z-score-scale-text"
                        style="left: {{ "%.2f" | format(tick.position) }}%;">{{ tick.value }}</div>
                    {% endfor %}
                  </div>

                  {% for parameter in z_score_plot.parameters %}
                    <div class="z-score-row">
                      <div class="z-score-plot-container">

                        {% for region in parameter.regions %}
                          <div class="z-score-region"
                            style="left: {{ "%.2f" | format(region.start_pos) }}%;
                              width: {{ "%.2f" | format(region.width) }}%;
                              background-color: {{ region.color }};"></div>
                        {% endfor %}

                        {% for tick in z_score_plot.axis_ticks %}
                          <div class="vertical-divider" style="left: {{ "%.2f" | format(tick.position) }}%;"></div>
                        {% endfor %}

                        {% for dot in parameter.dots %}
                          <div class="z-score-dot {{ dot.color_class }}"
                            style="left: {{ "%.2f" | format(dot.position) }}%;"
                            title="{{ dot.color_class|replace('bg-chart-1', 'Baseline')|replace('bg-chart-2', 'Post-BD') }} Z-score: {{ dot.value }}"></div>
                        {% endfor %}

                      </div>
                      <div class="z-score-plot-label">{{ parameter.description.lower() }}</div>
                    </div>
                  {% endfor %}

                </div>
              </div>
            </div>

            {% if rft.flowvolloop %}
              <div class="media">
                <div class="flow-bg"></div>
                <img class="flow-vol" src="data:image/png;base64,{{ rft.flowvolloop_base64 }}"
                  alt="Flow Volume Loop"
                >
              </div>
            {% endif %}

            <div class="report-text">
              <div class="report-header-inline">
                <div class="report-text-body">
                <span class="report-text-heading"
                  style="margin-right:2px">Technical Notes:</span> {{ rft.technicalnotes or '-' }}
                </div>
              </div>
            </div>

            <div class="report-text">
              <div class="report-text-body">
                <span class="report-text-heading">Report: </span>
                {{ rft.report_text or ''}}
              </div>

              <div class="report-reported-by">
                <span style="margin-right:2px">Reported by:</span> {{ report_reportedby or '-' }}
               {% if report_reporteddate %} <span>on</span> {{ report_reporteddate or'-' }} {% endif %}
              </div>

              <div class="report-reported-by"> Signature: __________________________</div>
            </div>
          </div>
        </div>
      {% else %}
        <div>
          {% if not rft.co_transfer.is_empty() or not rft.lung_volumes.is_empty() or not rft.feno.is_empty() or not rft.mrps.is_empty() %}
            <div style="display: flex">
              <div>
                {% if not rft.co_transfer.is_empty() %}
                  <div class="test-sp">
                    <div class="labels">
                      <div class="labels-heading">Co transfer factor</div>
                      <div class="labels-text">tlco (ml/min/mmHg)</div>
                      <div class="labels-text">tlcoHb (ml/min/mmHg)</div>
                      <div class="labels-text">va (L)</div>
                      <div class="labels-text">Kco (ml/min/mmHg/L)</div>
                      <div class="labels-text">KcoHb (ml/min/mmHg/L)</div>
                      <div class="labels-text">Vi (l)</div>
                      <div class="labels-text">Hb (gm/dl)</div>
                    </div>

                    <div>
                      <div class="baseline">{{ rft.co_transfer.condition.value or 'Baseline' }}</div>
                      <div class="table-body">
                        <div class="table-row">
                          <div class="table-cell">{{ rft.co_transfer.tlco.ref_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.tlco.formated_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.tlco.zscore }}</div>
                          <div class="table-cell">{{ rft.co_transfer.tlco.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.co_transfer.tlco.ref_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.tlco_hb }}</div>
                          <div class="table-cell">{{ rft.co_transfer.tlco_hb.zscore }}</div>
                          <div class="table-cell">{{ rft.co_transfer.tlco_hb.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.co_transfer.va.ref_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.va.formated_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.va.zscore }}</div>
                          <div class="table-cell">{{ rft.co_transfer.va.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.co_transfer.kco.ref_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.kco.formated_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.kco.zscore }}</div>
                          <div class="table-cell">{{ rft.co_transfer.kco.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.co_transfer.kco.ref_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.kco_hb }}</div>
                          <div class="table-cell">{{ rft.co_transfer.kco_hb.zscore }}</div>
                          <div class="table-cell">{{ rft.co_transfer.kco_hb.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.co_transfer.vi.ref_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.vi.formated_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.vi.zscore }}</div>
                          <div class="table-cell">{{ rft.co_transfer.vi.pred_percentage }}</div>
                        </div>

                        <div class="table-row">
                          <div class="table-cell">{{ rft.co_transfer.hb.ref_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.hb.formated_value }}</div>
                          <div class="table-cell">{{ rft.co_transfer.hb.zscore }}</div>
                          <div class="table-cell">{{ rft.co_transfer.hb.pred_percentage }}</div>
                        </div>

                      </div>
                    </div>
                  </div>
                {% endif %}
                {% if not rft.lung_volumes.is_empty() %}
                  <div class="test-sp">
                    <div class="labels">
                      <div class="labels-heading">Lung volumes</div>
                      <div class="labels-text">tlc (l)</div>
                      <div class="labels-text">lvvc (l)</div>
                      <div class="labels-text">ic (L)</div>
                      <div class="labels-text">frc (l)</div>
                      <div class="labels-text">erv (l)</div>
                      <div class="labels-text">rv (l)</div>
                      <div class="labels-text">rv/tlc (%)</div>
                    </div>

                    <div>
                      <div class="baseline">{{ rft.lung_volumes.condition.value or 'Baseline' }}
                        ({{ rft.lung_volumes.method.value or '' }})
                      </div>

                      <div class="table-body">
                        <div class="table-row">
                          <div class="table-cell">{{ rft.lung_volumes.tlc.ref_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.tlc.formated_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.tlc.zscore }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.tlc.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.lung_volumes.lvvc.ref_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.lvvc.formated_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.lvvc.zscore }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.lvvc.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.lung_volumes.ic.ref_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.ic.formated_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.ic.zscore }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.ic.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.lung_volumes.frc.ref_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.frc.formated_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.frc.zscore }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.frc.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.lung_volumes.erv.ref_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.erv.formated_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.erv.zscore }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.erv.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.lung_volumes.rv.ref_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.rv.formated_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.rv.zscore }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.rv.pred_percentage }}</div>
                        </div>

                        <div class="table-row">
                          <div class="table-cell">{{ rft.lung_volumes.rv_tlc.ref_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.rv_tlc.formated_value }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.rv_tlc.zscore }}</div>
                          <div class="table-cell">{{ rft.lung_volumes.rv_tlc.pred_percentage }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                {% endif %}
                {% if not rft.feno.is_empty() %}
                  <div class="test-sp">
                    <div class="labels">
                      <div class="labels-heading">exhaled nitric oxide</div>
                      <div class="labels-text">feno (ppb)</div>
                    </div>

                    <div>
                      <div class="baseline">{{ rft.feno.condition.value or 'Baseline' }}</div>

                      <div class="table-body">
                        <div class="table-row">
                          <div class="table-cell">{{ rft.feno.feno.ref_value }}</div>
                          <div class="table-cell">{{ rft.feno.feno.formated_value }}</div>
                          <div class="table-cell">{{ rft.feno.feno.zscore }}</div>
                          <div class="table-cell">{{ rft.feno.feno.pred_percentage }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                {% endif %}
                {% if not rft.mrps.is_empty() %}
                  <div class="test-sp">
                    <div class="labels">
                      <div class="labels-heading">MRPS</div>
                      <div class="labels-text">mip (cmh2O)</div>
                      <div class="labels-text">mep (cmh2O)</div>
                      <div class="labels-text">snip (cmh2O)</div>
                    </div>

                    <div>
                      <div class="baseline">{{ rft.mrps.condition.value or 'Baseline' }}</div>

                      <div class="table-body">
                        <div class="table-row">
                          <div class="table-cell">{{ rft.mrps.mip.ref_value }}</div>
                          <div class="table-cell">{{ rft.mrps.mip.formated_value }}</div>
                          <div class="table-cell">{{ rft.mrps.mip.zscore }}</div>
                          <div class="table-cell">{{ rft.mrps.mip.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.mrps.mep.ref_value }}</div>
                          <div class="table-cell">{{ rft.mrps.mep.formated_value }}</div>
                          <div class="table-cell">{{ rft.mrps.mep.zscore }}</div>
                          <div class="table-cell">{{ rft.mrps.mep.pred_percentage }}</div>
                        </div>
                        <div class="table-row">
                          <div class="table-cell">{{ rft.mrps.snip.ref_value }}</div>
                          <div class="table-cell">{{ rft.mrps.snip.formated_value }}</div>
                          <div class="table-cell">{{ rft.mrps.snip.zscore }}</div>
                          <div class="table-cell">{{ rft.mrps.snip.pred_percentage }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                {% endif %}
              </div>

              <div>
                <div class="media">
                  <div class="chart">
                    <!-- Legend -->
                    <div class="horizontal">
                      <div class="horizontal legend-text" style="margin-right: 6px">
                        <span class="green-dot"></span>{{ z_score_plot.sp1_condition or 'Baseline' }}
                      </div>
                      <div class="horizontal legend-text">
                        <span class="yellow-dot"></span>{{ z_score_plot.sp2_condition or 'Post BD (Salb MDI)' }}
                      </div>
                    </div>

                    <div class="z-score-plot">
                      <div class="z-score-scale">
                        {% for tick in z_score_plot.axis_ticks %}
                          <div class="z-score-scale-text"
                            style="left: {{ "%.2f" | format(tick.position) }}%;">{{ tick.value }}</div>
                        {% endfor %}
                      </div>

                      {% for parameter in z_score_plot.parameters %}
                        <div class="z-score-row">
                          <div class="z-score-plot-container">

                            {% for region in parameter.regions %}
                              <div class="z-score-region"
                                style="left: {{ "%.2f" | format(region.start_pos) }}%;
                                  width: {{ "%.2f" | format(region.width) }}%;
                                  background-color: {{ region.color }};"></div>
                            {% endfor %}

                            {% for tick in z_score_plot.axis_ticks %}
                              <div class="vertical-divider" style="left: {{ "%.2f" | format(tick.position) }}%;"></div>
                            {% endfor %}

                            {% for dot in parameter.dots %}
                              <div class="z-score-dot {{ dot.color_class }}"
                                style="left: {{ "%.2f" | format(dot.position) }}%;"
                                title="{{ dot.color_class|replace('bg-chart-1', 'Baseline')|replace('bg-chart-2', 'Post-BD') }} Z-score: {{ dot.value }}"></div>
                            {% endfor %}

                          </div>
                          <div class="z-score-plot-label">{{ parameter.description.lower() }}</div>
                        </div>
                      {% endfor %}

                    </div>
                  </div>
                </div>

                {% if rft.flowvolloop %}
                  <div class="media">
                    <div class="flow-bg"></div>
                    <img class="flow-vol" src="data:image/png;base64,{{ rft.flowvolloop_base64 }}"
                      alt="Flow Volume Loop"
                    >
                  </div>
                {% endif %}
              </div>
            </div>
          {% endif %}

          <!-- Full width report section when no blood gases -->
          <div style="margin-top: 12px;">
            <div class="report-text-full-width">
              <div class="report-header-inline">
                <div class="report-text-body">
                <span class="report-text-heading"
                  style="margin-right:2px">Technical Notes:</span> {{ rft.technicalnotes or '-' }}
                </div>
              </div>
            </div>

            <div class="report-text-full-width">
              <div class="report-text-body">
                <div class="report-text-heading" style="margin-bottom:4px">Report</div>
                {{ rft.report_text or '' }}
              </div>
              <div class="report-reported-by">
                <span style="margin-right:2px">Reported by:</span> {{ rft.report_reportedby or '-' }}
                {% if rft.report_reporteddate %} <span>on</span> {{ rft.report_reporteddate }} {% endif %}
              </div>
              <div class="report-reported-by" style="margin-top:8px"> Signature: __________________________</div>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
    {% set sources_by_test = rft.get_sources_by_test() %}
    {% include 'reports/shared/report-footer.html' %}
  </body>
</html>
