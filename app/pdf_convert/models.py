import uuid
from datetime import date

from app.normal_values.utils import RftGender
from faker import Faker
from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, ForeignKey, Date, UUID, func
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship
from datetime import datetime

from dateutil.relativedelta import relativedelta

from app import db

# Initialize Faker
fake = Faker()


class PasPt(db.Model):
    __tablename__ = 'pas_pt'

    patientid = Column(Integer, primary_key=True)
    ur = Column(String, nullable=False)
    ur_hsid = Column(String, nullable=False)
    dob = Column(Date)
    gender_code = Column(String)
    gender_forrfts_code = Column(String)
    email = Column(String)
    phone_home = Column(String)
    phone_mobile = Column(String)
    phone_work = Column(String)
    countryofbirth_code = Column(String)
    preferredlanguage_code = Column(String)
    aboriginalstatus_code = Column(String)
    medicare_no = Column(String)
    medicare_expirydate = Column(String)
    death_indicator = Column(String)
    death_date = Column(String)
    race_forrfts_code = Column(String)
    site_id = Column(Integer)
    # created = db.Column(db.DateTime, server_default=func.now())
    # updated = db.Column(db.DateTime, server_default=func.now())
    names = relationship("PasPtName", back_populates="patient", cascade="all, delete-orphan")
    addresses = relationship("PasPtAddress", back_populates="patient", cascade="all, delete-orphan")
    ur_numbers = relationship("PasPtUrNumber", back_populates="patient", cascade="all, delete-orphan")

    @hybrid_property
    def full_name(self):
        if not self.names:
            return ""

        name_obj = self.names[0]

        name_parts = []
        if name_obj.surname:
            name_parts.append(name_obj.surname)
        if name_obj.firstname:
            name_parts.append(name_obj.firstname)

        full_name_str = ", ".join(name_parts)

        if name_obj.title:
            return f"{name_obj.title}. {full_name_str}"

        return full_name_str

    @hybrid_property
    def rft_gender(self):
        return RftGender.from_code(self.gender_forrfts_code)


    @hybrid_property
    def is_deceased(self):
        if not self.death_date:
            return False
        try:
            # death_date is a string, assuming 'YYYY-MM-DD' or ISO 8601 format
            death_date_obj = date.fromisoformat(self.death_date)
            return death_date_obj <= date.today()
        except (ValueError, TypeError):
            return False

    @is_deceased.expression
    def is_deceased(cls):
        return (cls.death_date != None) & (cls.death_date <= func.current_date())

    @hybrid_property
    def age_today(self):
        if not self.dob:
            return None

        today = date.today()
        rd = relativedelta(today, self.dob)
        return (rd.years * 12 + rd.months) / 12

    @age_today.expression
    def age_today(cls):
        return ((func.extract('year', func.current_date()) - func.extract('year', cls.dob)) * 12 +
                (func.extract('month', func.current_date()) - func.extract('month', cls.dob))) / 12.0

    def age_at_date(self, date_at: date):
        rd = relativedelta(date_at, self.dob)
        return (rd.years * 12 + rd.months) / 12

    @classmethod
    def create_fake(cls, site_id):
        """Create a fake PasPt patient with related data"""

        gender = 'M'  # fake.random_element(elements=('M', 'F'))

        # Create the main patient record
        patient = cls(
            ur=fake.unique.random_number(digits=8),
            ur_hsid=fake.unique.random_number(digits=10),
            dob=date(1957, 6, 22),
            gender_code=gender,
            gender_forrfts_code='1' if gender == 'M' else '2',
            email=fake.email(),
            phone_home=fake.phone_number(),
            aboriginalstatus_code='9',
            death_indicator='N',
            death_date=None,
            race_forrfts_code='1',
            site_id=site_id
        )

        # Add to session and flush to get the patientid
        db.session.add(patient)
        db.session.flush()

        # Create related name record
        name = PasPtName.create_fake(patient.patientid)
        patient.names.append(name)

        # Create related address record
        address = PasPtAddress.create_fake(patient.patientid)
        patient.addresses.append(address)

        # Create related UR number record
        ur_number = PasPtUrNumber.create_fake(patient.patientid, patient.ur, patient.ur_hsid)
        patient.ur_numbers.append(ur_number)

        return patient


class PasPtName(db.Model):
    __tablename__ = 'pas_pt_names'

    nameid = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    name_type = Column(String, default='primary')
    title = Column(String)
    firstname = Column(String, nullable=False)
    surname = Column(String, nullable=False)
    middlename = Column(String)

    patient = relationship("PasPt", back_populates="names")

    @classmethod
    def create_fake(cls, patientid, gender=None):
        """Create a fake PasPtName record"""

        if not gender:
            gender = fake.random_element(elements=('M', 'F'))

        if gender == 'M':
            title = fake.random_element(elements=('Mr', None))
            firstname = fake.first_name_male()
            lastname = fake.last_name_male()
        else:
            title = fake.random_element(elements=('Mrs', 'Ms'))
            firstname = fake.first_name_female()
            lastname = fake.last_name_female()

        return cls(
            patientid=patientid,
            name_type=fake.random_element(elements=('primary',)),
            title=title,
            firstname=firstname,
            surname=lastname,
        )


class PasPtAddress(db.Model):
    __tablename__ = 'pas_pt_address'

    addressid = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    address_type_code = Column(String, default='primary')
    address_1 = Column(String)
    address_2 = Column(String)
    suburb = Column(String)
    postcode = Column(String)

    patient = relationship("PasPt", back_populates="addresses")

    @classmethod
    def create_fake(cls, patientid):
        """Create a fake PasPtAddress record"""
        return cls(
            patientid=patientid,
            address_type_code=fake.random_element(elements=('4', '1', '2', '3')),
            address_1=fake.street_address(),
            address_2=fake.secondary_address() if fake.boolean(chance_of_getting_true=20) else None,
            suburb=fake.city(),
            postcode=fake.postcode()
        )


class DeviceTemplate(db.Model):
    __tablename__ = 'device_templates'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    filename = Column(Text, nullable=False)
    template_s3_key = Column(Text, nullable=False, unique=True)
    instruction_s3_key = Column(Text, nullable=True)
    manufacturer = Column(String, nullable=False)
    device_model = Column(String, nullable=False)
    template_type = Column(String, nullable=False)


class PasPtUrNumber(db.Model):
    __tablename__ = 'pas_pt_ur_numbers'

    ur_id = Column(Integer, primary_key=True)
    patientid = Column(Integer, ForeignKey('pas_pt.patientid'), nullable=False)
    ur = Column(String, nullable=False)
    ur_hsid = Column(String, nullable=False)
    ur_status = Column(String, nullable=False)

    patient = relationship("PasPt", back_populates="ur_numbers")

    @classmethod
    def create_fake(cls, patientid, ur, ur_hsid):
        """Create a fake PasPtUrNumber record"""
        return cls(
            patientid=patientid,
            ur=ur,
            ur_hsid=ur_hsid,
            ur_status='primary'
        )


class AttachmentFile(db.Model):
    __tablename__ = 'attachment_files'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    site_id = Column(Integer, nullable=False)
    patient_id = Column(Integer, nullable=True)
    rft_id = Column(Integer, nullable=True)
    filename = Column(Text, nullable=False)
    s3_key = Column(Text, nullable=False, unique=True)
    uploaded_by = Column(Text, nullable=True)
    uploaded_at = Column(TIMESTAMP(timezone=False), server_default=func.now())
    description = Column(Text, nullable=True)
