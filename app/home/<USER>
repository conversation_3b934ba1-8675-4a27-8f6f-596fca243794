"""General page routes."""
from flask import Blueprint, send_from_directory, render_template_string, current_app
from werkzeug.exceptions import NotFound
import os
import json


home_bp = Blueprint(
    "home_bp",
    __name__,
    static_url_path="/",
    static_folder=None
)


@home_bp.route('/', defaults={'path': ''})
@home_bp.route('/<path:path>')
def catch_all(path):
    vite_env_vars = {key: value for key, value in os.environ.items()
                     if key.startswith('VITE_APP_')}

    register_enabled = current_app.config.get('REGISTER_TRIAL_ENABLED')
    if register_enabled is not None:
        vite_env_vars['REGISTER_TRIAL_ENABLED'] = register_enabled

    try:
        return send_from_directory('app-static', path)
    except NotFound:
        with open(os.path.join('app/app-static', 'index.html')) as f:
            template = f.read()

        return render_template_string(template, vite_env_vars=json.dumps(vite_env_vars))
