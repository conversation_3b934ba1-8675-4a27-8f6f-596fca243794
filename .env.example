MAIL_USERNAME=changeme
MAIL_PASSWORD=changeme
MAIL_SERVER=smtp.office365.com
MAIL_PORT=587
MAIL_USE_TLS=True
TEXTMAGIC_KEY=changeme
TEXTMAGIC_USERNAME=changeme
HIDDEN_ITEMS=leads,scheduler,marketing,diagnosis,external_report,todo,medication,study,participant_details
APP_TYPE=DEV
SUPPRESS_EMAIL=False
ENABLE_SMS=True

# Used by flask app
DATABASE_URL=************************************/rezibase
SITE_NAME=EMERITUS
FLASK_PORT=8000
REGISTER_TRIAL_ENABLED=True  # Toggle trial registration sign up feature

# These variables are injected to FE when served from flask app
# Not used when running FE in dev mode (ie. pnpm run dev)
VITE_APP_ADMIN_URL=/admin/
VITE_APP_HASURA_URL=https://rezibase-web-staging-hasura-ef7ff331b079.herokuapp.com/v1/graphql
VITE_APP_SUPERUSER_DOMAINS=@carteblanche.tech,@cardiobase.com
VITE_APP_AG_GRID_LICENSE_KEY=

# For local db only
POSTGRES_DB=rezibase
POSTGRES_USER=rezibase
POSTGRES_PASSWORD=rezibase

# Used by flask app
# When running locally avoid using $ in this variable
JWT_SECRET_KEY=TEST_SECRET_KEY

# For local hasura instance
HASURA_GRAPHQL_METADATA_DATABASE_URL=postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
HASURA_GRAPHQL_ENABLE_CONSOLE="true"
HASURA_GRAPHQL_DEV_MODE="true"
HASURA_GRAPHQL_ENABLED_LOG_TYPES=startup, http-log, webhook-log, websocket-log, query-log
HASURA_WEBHOOK_URL=http://web:${FLASK_PORT}/webhook/activity
# Please use the same secret key in FE/.env
HASURA_GRAPHQL_ADMIN_SECRET=myadminsecretkey
HASURA_GRAPHQL_METADATA_DEFAULTS={"backend_configs":{"dataconnector":{"athena":{"uri":"http://data-connector-agent:8081/api/v1/athena"},"mariadb":{"uri":"http://data-connector-agent:8081/api/v1/mariadb"},"mysql8":{"uri":"http://data-connector-agent:8081/api/v1/mysql"},"oracle":{"uri":"http://data-connector-agent:8081/api/v1/oracle"},"snowflake":{"uri":"http://data-connector-agent:8081/api/v1/snowflake"}}}}

HASURA_GRAPHQL_JWT_SECRET={"type": "HS256", "key": "$JWT_SECRET_KEY"}

MAILCHIMP_API=
MAILCHIMP_LIST=

AWS_ACCESS_KEY=
AWS_SECRET_KEY=
AWS_S3_BUCKET=

